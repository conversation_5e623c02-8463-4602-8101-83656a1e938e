<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/scan.php';
require_once __ROOT__ . '/src/classes/sector.php';
require_once __ROOT__ . '/src/classes/tickerload.php';
require_once __ROOT__ . '/src/general/preparar.php';


$lastscan = Scan::get_lastadd($conexion);
$msj = "Last ticker added: ".$lastscan->ticker." on ".format_date($lastscan->date);

if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $date = create_date();
        
        if (isset($_GET['ids'])) {
            $ID_sector = $_GET['ids'];
        }

        $tickerload = TickerLoad::get($conexion);

        if($tickerload){
            $ticker = $tickerload->ticker;
            $ID_sector = $tickerload->id_sector;

            $ntickersbysector = TickerLoad::get_positionbysector($ID_sector,$conexion);
            $ntickers = TickerLoad::get_position($conexion);

            require_once __ROOT__ . '/src/iscansload.php';
        }
    } catch (Exception $e) {
        $error_display = 'block';
        $error = $e->getMessage();
    }
}

if ($_SERVER['REQUEST_METHOD'] == 'POST' && (isset($_POST['sub_save']) || isset($_POST['sub_saveadd']))) {
    try {
        //add new fields in 2 sections @tickerload
        $date = limpiar_datos($_POST['date']);
        $ticker = limpiar_datos($_POST['ticker']);
        $ID_sector = limpiar_datos($_POST['ID_sector']);
        $is20matrendup = @getvalue_checkbox($_POST['is20matrendup']);
        $is50matrendup = @getvalue_checkbox($_POST['is50matrendup']);
        $is200matrendup = @getvalue_checkbox($_POST['is200matrendup']);
        $uncut200ma1m = @getvalue_checkbox($_POST['uncut200ma1m']);
        $uncut200ma5m = @getvalue_checkbox($_POST['uncut200ma5m']);
        $hgvolredd7d = @getvalue_checkbox($_POST['hgvolredd7d']);
        $hgvolredd30d = @getvalue_checkbox($_POST['hgvolredd30d']);
        $hgvolredd60d = @getvalue_checkbox($_POST['hgvolredd60d']);
        $crebase = @getvalue_checkbox($_POST['crebase']);
        $overbase = @getvalue_checkbox($_POST['overbase']);
        $datetopbase = limpiar_datos($_POST['datetopbase']);
        $basedepth = limpiar_datos($_POST['basedepth']);
        $cureps = limpiar_datos($_POST['cureps']);
        $eps3qyoy = limpiar_datos($_POST['eps3qyoy']);
        $eps2qyoy = limpiar_datos($_POST['eps2qyoy']);
        $eps1qyoy = limpiar_datos($_POST['eps1qyoy']);
        $sales3qyoy = limpiar_datos($_POST['sales3qyoy']);
        $sales2qyoy = limpiar_datos($_POST['sales2qyoy']);
        $sales1qyoy = limpiar_datos($_POST['sales1qyoy']);
        $compare = @getvalue_checkbox($_POST['compare']);
        $standby = @getvalue_checkbox($_POST['standby']);
        $standbyreason = limpiar_datos($_POST['standbyreason']);
        $is20maabvs50ma = @getvalue_checkbox($_POST['is20maabvs50ma']);
        $lastwave = limpiar_datos($_POST['lastwave']);
        $numberwaves = limpiar_datos($_POST['numberwaves']);
        $earningssurprise3q = @getvalue_checkbox($_POST['earningssurprise3q']);
        $earningssurprise2q = @getvalue_checkbox($_POST['earningssurprise2q']);
        $earningssurprise1q = @getvalue_checkbox($_POST['earningssurprise1q']);
        $islowmarketvolatility = @getvalue_checkbox($_POST['islowmarketvolatility']);
        //add new fields in 2 sections @tickerload

        validar_textovacio($date, 'Specify date.');
        validar_textovacio($ticker, 'Specify ticker.');
        validar_textovacio($ID_sector, 'Specify sector.');

        $scan = new Scan;
        $scan->date = $date;
        $scan->ticker = $ticker;
        $scan->ID_sector = $ID_sector;
        $scan->is20matrendup = $is20matrendup;
        $scan->is50matrendup = $is50matrendup;
        $scan->is200matrendup = $is200matrendup;
        $scan->uncut200ma1m = $uncut200ma1m;
        $scan->uncut200ma5m = $uncut200ma5m;
        $scan->hgvolredd7d = $hgvolredd7d;
        $scan->hgvolredd30d = $hgvolredd30d;
        $scan->hgvolredd60d = $hgvolredd60d;
        $scan->failbreakbase = 0;
        $scan->crebase = $crebase;
        $scan->overbase = $overbase;
        $scan->datetopbase = $datetopbase;
        $scan->basedepth = ($basedepth == "") ? 0 : $basedepth;
        $scan->t1depth = 0;
        $scan->t1buyvol = 0;
        $scan->t1sellvol = 0;
        $scan->t2depth = 0;
        $scan->t2buyvol = 0;
        $scan->t2sellvol = 0;
        $scan->t3depth = 0;
        $scan->t3buyvol = 0;
        $scan->t3sellvol = 0;
        $scan->t4depth = 0;
        $scan->t4buyvol = 0;
        $scan->t4sellvol = 0;
        $scan->t5depth = 0;
        $scan->t5buyvol = 0;
        $scan->t5sellvol = 0;
        $scan->cureps = ($cureps == "") ? 0 : $cureps;
        $scan->eps3qyoy = ($eps3qyoy == "") ? 0 : $eps3qyoy;
        $scan->eps2qyoy = ($eps2qyoy == "") ? 0 : $eps2qyoy;
        $scan->eps1qyoy = ($eps1qyoy == "") ? 0 : $eps1qyoy;
        $scan->sales3qyoy = ($sales3qyoy == "") ? 0 : $sales3qyoy;
        $scan->sales2qyoy = ($sales2qyoy == "") ? 0 : $sales2qyoy;
        $scan->sales1qyoy = ($sales1qyoy == "") ? 0 : $sales1qyoy;
        $scan->compare = $compare;
        $scan->standby = $standby;
        $scan->standbyreason = $standbyreason;
        $scan->rsi = 0;
        $scan->is20maabvs50ma = $is20maabvs50ma;
        $scan->lastwave = $lastwave;
        $scan->numberwaves = $numberwaves;
        $scan->earningssurprise3q = $earningssurprise3q;
        $scan->earningssurprise2q = $earningssurprise2q;
        $scan->earningssurprise1q = $earningssurprise1q;        
        $scan->islowmarketvolatility = $islowmarketvolatility;        
        $scan->save($conexion);

        TickerLoad::update($scan->ticker,$conexion);

        if (isset($_POST['sub_save'])) {
            header('Location: scans?ids='.$scan->ID_sector."&ticker=".strtoupper($ticker));
        } else {
            header('Location: iscans?ids='.$scan->ID_sector);
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
} elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_load'])) {
    try {
        $ticker = limpiar_datos($_POST['ticker']);
        $ID_sector = limpiar_datos($_POST['ID_sector']);

        validar_textovacio($ticker,"Specify ticker");

        $date = create_date();

        $ntickersbysector = 0;
        $ntickers = 0;

        require_once __ROOT__ . '/src/iscansload.php';
        
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}  elseif ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_discard'])) {
    try {
        $ticker = limpiar_datos($_POST['ticker']);
        $ID_sector = limpiar_datos($_POST['ID_sector']);

        TickerLoad::discard($ticker,$conexion);

        header('Location: iscans?ids='.$scan->ID_sector);
        
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}

$sectors = Sector::get_list($conexion);

require_once __ROOT__ . '/views/iscans.view.php';

?>