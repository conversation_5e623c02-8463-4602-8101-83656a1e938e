<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';
require_once __ROOT__ . '/src/classes/pais.php';
require_once __ROOT__ . '/src/general/preparar.php';

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

        if (isset($_SESSION['idpartido'])) {
            $idpartido = $_SESSION['idpartido'];

            // logic:

            unset($_SESSION['idpartido']);
        } else {
            header('Location: lbudgets');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $idpartido = limpiar_datos($_POST['idpartido']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_mod
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_mod'])) {
    try {
        $method_mod = 1;

        $modpartido = new Partido();        
        $modpartido->id = $idpartido;
        $modpartido->home = limpiar_datos($_POST['home']);
        $modpartido->away = limpiar_datos($_POST['away']);
        $modpartido->pais = limpiar_datos($_POST['pais']);
        $modpartido->fecha = limpiar_datos($_POST['fecha']);
        $modpartido->hora = limpiar_datos($_POST['hora']);
        $modpartido->modify($conexion);

        header('Location: lpartidos?m=1');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_mod
#region try
try {
    $method_try = 1;

    $modpartido = Partido::get($idpartido, $conexion);
    $apuestastipos = ApuestaTipo::getList(array(),$conexion);
    $paises = Pais::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/epartido.view.php';

?>
