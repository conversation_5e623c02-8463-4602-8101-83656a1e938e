<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/remindertodo.php';
require_once __ROOT__ . '/src/general/preparar.php';

$newremindertodo = new ReminderTodo();

#region sub_add
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $newremindertodo->description = limpiar_datos($_POST['description']);
        $newremindertodo->tiempo = limpiar_datos($_POST['tiempo']);
        $newremindertodo->frequencia = limpiar_datos($_POST['frequencia']);
        $newremindertodo->frequenciarecordar = limpiar_datos($_POST['frequenciarecordar']);
        $newremindertodo->add($conexion);

        header('Location: lreminderstodo?i=1');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_add

require_once __ROOT__ . '/views/iremindertodo.view.php';

?>