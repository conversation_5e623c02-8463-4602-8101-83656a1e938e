<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/remindertodo.php';
require_once __ROOT__ . '/src/emails/sendemails.php';
require_once __ROOT__ . '/src/general/preparar.php';

$reminderstodo = array();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_GET['i'])) {
            $success_display = 'show';
            $success_text = 'Reminder created.';
        }
        if (isset($_GET['m'])) {
            $success_display = 'show';
            $success_text = 'Reminder modified.';
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region sub_changehecho
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_changehecho'])) {
    try {
        $mdlchangehecho_id_remindertodo = limpiar_datos($_POST['mdlchangehecho_id_remindertodo']);
        
        ReminderTodo::modifyHecho($mdlchangehecho_id_remindertodo, $conexion);

        $success_display = 'show';
        $success_text = 'Modification successful.';
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_changehecho
#region sub_selremindertodo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_selremindertodo'])) {
    try {
        $sel_id_remindertodo = limpiar_datos($_POST['sel_id_remindertodo']);

        $_SESSION['id_remindertodo'] = $sel_id_remindertodo;

        header('Location: eremindertodo');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_selremindertodo
#region sub_delremindertodo
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delremindertodo'])) {
    try {
        $didremindertodo = limpiar_datos($_POST['mdl_dremindertodo_idremindertodo']);

        ReminderTodo::delete($didremindertodo,$conexion);

        $success_display = 'show';
        $success_text = 'El reminder ha sido eliminado.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_delremindertodo
#region sub_changesleep
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_changesleep'])) {
    try {
        $cidremindertodo = limpiar_datos($_POST['mdl_changesleep_idremindertodo']);

        ReminderTodo::modifySleep($cidremindertodo, $conexion);

        $success_display = 'show';
        $success_text = 'El estado sleep ha sido modificado.';

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_changesleep
#region sub_sleepall
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_sleepall'])) {
    try {
        ReminderTodo::modifySleepAll($conexion);

        $success_display = 'show';
        $success_text = 'All reminders are sleep.';
				
	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_sleepall
#region try
try {
    $reminderstodo = ReminderTodo::getList($conexion);

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lreminderstodo.view.php';

?>











