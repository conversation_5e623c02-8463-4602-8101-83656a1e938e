<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/partidoapuesta.php';
require_once __ROOT__ . '/src/classes/config.php';
require_once __ROOT__ . '/src/general/preparar.php';

$riesgo = 0;
$riesgo_porc = 0;

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        $method_get = 1;

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $method_postsolo = 1;
        
        $matchup = limpiar_datos($_POST['matchup']);
        $riesgo = limpiar_datos($_POST['riesgo']);
        $potencial = limpiar_datos($_POST['potencial']);
        $winprobabilityporc = limpiar_datos($_POST['winprobabilityporc']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion postsolo
#region sub_calcular
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_calcular'])) {
    try {
        $method_sub_calcular = 1;
        
        $resultado = PartidoApuesta::calculateBetSize($potencial, $winprobabilityporc, $conexion);
        $riesgo = $resultado['betsize_value'];
        $riesgo_porc = $resultado['betsize_porc'];

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_calcular
#region sub_crear
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_crear'])) {
    try {
        $method_sub_crear = 1;
        
        $_SESSION['loadmatchup'] = 1;
        $_SESSION['matchup'] = $matchup;
        $_SESSION['riesgo'] = $riesgo;
        $_SESSION['riesgo_porc'] = $riesgo_porc;
        $_SESSION['potencial'] = $potencial;
        $_SESSION['winprobabilityporc'] = $winprobabilityporc;

        header('Location: ipartido');
        exit();

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion sub_crear
#region try
try {
    $method_try = 1;

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/ckellycalculator.view.php';

?>