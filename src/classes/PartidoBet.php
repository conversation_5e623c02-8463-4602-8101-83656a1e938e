<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a partido bet record.
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class PartidoBet
{
    // --- Attributes ---
    private ?string $id = null; // Stores the 'desordenado' ID
    private ?string $fecha_creado = null; // DATETIME format 'YYYY-MM-DD HH:MM:SS'
    private ?float $valor_apostado = null;
    private ?float $cuota = null;
    private ?int $es_cerrado = null; // TINYINT(1) DEFAULT 0
    private ?int $es_ganado = null; // TINYINT(1) DEFAULT 0
    private ?float $valor_recibido = null; // DEFAULT 0
    private ?float $valor_profit = null; // DEFAULT 0
    private ?int $estado = null; // TINYINT(1) DEFAULT 1

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id = null;
        $this->fecha_creado = null;
        $this->valor_apostado = null;
        $this->cuota = null;
        $this->es_cerrado = null;
        $this->es_ganado = null;
        $this->valor_recibido = null;
        $this->valor_profit = null;
        $this->estado = null;
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of PartidoBet.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            
            // The ID from the DB ('id') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                    error_log("desordena() returned empty/null for DB ID: " . $dbId);
                    throw new Exception("Error processing PartidoBet ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }

            $objeto->fecha_creado = $data['fecha_creado'] ?? null;
            $objeto->valor_apostado = isset($data['valor_apostado']) ? (float)$data['valor_apostado'] : null;
            $objeto->cuota = isset($data['cuota']) ? (float)$data['cuota'] : null;
            $objeto->es_cerrado = isset($data['es_cerrado']) ? (int)$data['es_cerrado'] : null;
            $objeto->es_ganado = isset($data['es_ganado']) ? (int)$data['es_ganado'] : null;
            $objeto->valor_recibido = isset($data['valor_recibido']) ? (float)$data['valor_recibido'] : null;
            $objeto->valor_profit = isset($data['valor_profit']) ? (float)$data['valor_profit'] : null;
            $objeto->estado = isset($data['estado']) ? (int)$data['estado'] : null;

            return $objeto;

        } catch (Exception $e) {
            throw new Exception("Failed to construct PartidoBet from data: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a PartidoBet object from the database by its 'desordenado' string ID.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBet to retrieve.
     * @param PDO $conexion The database connection object.
     * @return self|null A PartidoBet object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM partidos_bets
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            if ($resultado === false) {
                return null; // No record found
            }

            return self::construct($resultado);

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBet: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active PartidoBet objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of PartidoBet objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM partidos_bets
            WHERE estado = :estado
            ORDER BY fecha_creado DESC
            SQL;

            $statement = $conexion->prepare($query);
            // Assuming 1 means active
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $partidoBets = [];
            foreach ($resultados as $resultado) {
                $partidoBets[] = self::construct($resultado);
            }

            return $partidoBets;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving PartidoBet list: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of PartidoBet records for the betting history page.
     * Includes associated PartidoBetDetalle records with match and bet type information.
     * Filters by active records (estado=1). If fecha is provided, filters by that date, otherwise shows all records.
     *
     * @param PDO $conexion The database connection object.
     * @param array $filters Optional filters: ['fecha' => 'Y-m-d', 'estado' => 1]
     * @return array An array of associative arrays containing PartidoBet data with associated details.
     * @throws Exception If there is an error during the database query.
     */
    public static function getListForBetsRealizados(PDO $conexion, array $filters = []): array
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            // Set default filters
            $estado = $filters['estado'] ?? 1; // Active records by default
            $fecha = $filters['fecha'] ?? null; // No date filter by default - show all records

            // Build the WHERE clause dynamically
            $whereClause = "WHERE pb.estado = :estado";
            if ($fecha !== null) {
                $whereClause .= " AND DATE(pb.fecha_creado) = :fecha";
            }

            $query = <<<SQL
            SELECT
                pb.id as pb_id,
                pb.fecha_creado,
                pb.valor_apostado,
                pb.cuota as pb_cuota,
                pb.es_cerrado,
                pb.es_ganado,
                pb.valor_recibido,
                pb.valor_profit,
                pb.estado,
                -- Associated PartidoBetDetalle records
                GROUP_CONCAT(
                    CONCAT(
                        pbd.id, '|',
                        COALESCE(p.fecha, ''), '|',
                        COALESCE(p.home, ''), '|',
                        COALESCE(p.away, ''), '|',
                        COALESCE(p.pais, ''), '|',
                        COALESCE(at.nombre, ''), '|',
                        COALESCE(pbd.cuota, '')
                    ) SEPARATOR '||'
                ) as detalles_info
            FROM partidos_bets pb
            LEFT JOIN partidos_bets_detalles pbd ON pb.id = pbd.id_partido_bet
            LEFT JOIN partidos p ON pbd.id_partido = p.id_partido
            LEFT JOIN apuestas_tipos at ON pbd.id_apuesta_tipo = at.id_apuesta_tipo
            $whereClause
            GROUP BY pb.id, pb.fecha_creado, pb.valor_apostado, pb.cuota, pb.es_cerrado, pb.es_ganado, pb.valor_recibido, pb.valor_profit, pb.estado
            ORDER BY pb.fecha_creado DESC
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', $estado, PDO::PARAM_INT);
            if ($fecha !== null) {
                $statement->bindValue(':fecha', $fecha, PDO::PARAM_STR);
            }
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            // Process results to convert IDs and parse details
            foreach ($resultados as &$resultado) {
                // Convert PartidoBet ID to desordenado format
                if ($resultado['pb_id'] !== null) {
                    $resultado['pb_id'] = desordena($resultado['pb_id']);
                }

                // Parse associated details
                $resultado['detalles'] = [];
                if (!empty($resultado['detalles_info'])) {
                    $detallesArray = explode('||', $resultado['detalles_info']);
                    foreach ($detallesArray as $detalleInfo) {
                        $parts = explode('|', $detalleInfo);
                        if (count($parts) >= 7) {
                            $resultado['detalles'][] = [
                                'id' => desordena($parts[0]),
                                'fecha_partido' => $parts[1],
                                'home' => $parts[2],
                                'away' => $parts[3],
                                'pais' => $parts[4],
                                'tipo_apuesta' => $parts[5],
                                'cuota' => (float)$parts[6]
                            ];
                        }
                    }
                }
                unset($resultado['detalles_info']); // Remove the raw concatenated data
            }

            return $resultados;

        } catch (PDOException $e) {
            throw new Exception("Database error while retrieving betting history: " . $e->getMessage());
        }
    }

    /**
     * Saves (inserts or updates) the current PartidoBet instance to the database.
     * Sets the 'estado' to 1 (active) implicitly during insert if not set.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("PartidoBet::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("PartidoBet::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));

        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            if ($isInsertOperation) {
                return $this->_insert($conexion);
            } else {
                return $this->_update($conexion);
            }
        } catch (PDOException $e) {
            error_log("PartidoBet::guardar() - PDO Error: " . $e->getMessage());
            throw new Exception("Database error during save operation: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current PartidoBet instance into the database. (Private method)
     * Sets estado to 1 (active) by default on insert.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Assume new bets are active by default
        $estadoParaGuardar = $this->getEstado() ?? 1;
        $esCerradoParaGuardar = $this->getEsCerrado() ?? 0;
        $esGanadoParaGuardar = $this->getEsGanado() ?? 0;
        $valorRecibidoParaGuardar = $this->getValorRecibido() ?? 0.0;
        $valorProfitParaGuardar = $this->getValorProfit() ?? 0.0;

        $query = <<<SQL
        INSERT INTO partidos_bets (
            fecha_creado,
            valor_apostado,
            cuota,
            es_cerrado,
            es_ganado,
            valor_recibido,
            valor_profit,
            estado
        ) VALUES (
            :fecha_creado,
            :valor_apostado,
            :cuota,
            :es_cerrado,
            :es_ganado,
            :valor_recibido,
            :valor_profit,
            :estado
        )
        SQL;

        $statement = $conexion->prepare($query);

        $fechaActual = $this->getFechaCreado() ?? date('Y-m-d H:i:s');

        $statement->bindValue(':fecha_creado', $fechaActual, PDO::PARAM_STR);
        $statement->bindValue(':valor_apostado', $this->getValorApostado(), PDO::PARAM_STR);
        $statement->bindValue(':cuota', $this->getCuota(), PDO::PARAM_STR);
        $statement->bindValue(':es_cerrado', $esCerradoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':es_ganado', $esGanadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':valor_recibido', $valorRecibidoParaGuardar, PDO::PARAM_STR);
        $statement->bindValue(':valor_profit', $valorProfitParaGuardar, PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID and convert it to 'desordenado' format
            $lastInsertId = $conexion->lastInsertId();
            $desordenadoId = desordena($lastInsertId);
            if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                error_log("desordena() returned empty/null for inserted ID: " . $lastInsertId);
                throw new Exception("Error processing inserted PartidoBet ID. desordena() failed.");
            }
            $this->setId($desordenadoId);
            error_log("PartidoBet inserted successfully with ID: " . $this->getId());
        } else {
            error_log("Failed to insert PartidoBet: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current PartidoBet instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update PartidoBet without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
        if ($idOrdenado === false || $idOrdenado <= 0) {
            throw new Exception("Failed to process the PartidoBet ID for update: " . $this->getId());
        }

        // Use current estado if set, otherwise default to 1 (active)
        $estadoParaGuardar = $this->getEstado() ?? 1;
        $esCerradoParaGuardar = $this->getEsCerrado() ?? 0;
        $esGanadoParaGuardar = $this->getEsGanado() ?? 0;
        $valorRecibidoParaGuardar = $this->getValorRecibido() ?? 0.0;
        $valorProfitParaGuardar = $this->getValorProfit() ?? 0.0;

        $query = <<<SQL
        UPDATE partidos_bets SET
             fecha_creado = :fecha_creado
            ,valor_apostado = :valor_apostado
            ,cuota = :cuota
            ,es_cerrado = :es_cerrado
            ,es_ganado = :es_ganado
            ,valor_recibido = :valor_recibido
            ,valor_profit = :valor_profit
            ,estado = :estado
        WHERE id = :id
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':fecha_creado', $this->getFechaCreado(), PDO::PARAM_STR);
        $statement->bindValue(':valor_apostado', $this->getValorApostado(), PDO::PARAM_STR);
        $statement->bindValue(':cuota', $this->getCuota(), PDO::PARAM_STR);
        $statement->bindValue(':es_cerrado', $esCerradoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':es_ganado', $esGanadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':valor_recibido', $valorRecibidoParaGuardar, PDO::PARAM_STR);
        $statement->bindValue(':valor_profit', $valorProfitParaGuardar, PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update PartidoBet: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Deletes (soft deletes) a PartidoBet record by setting its estado to 0.
     *
     * @param string $id The 'desordenado' string ID of the PartidoBet to delete.
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            UPDATE partidos_bets
            SET estado = :estado
            WHERE id = :id
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', 0, PDO::PARAM_INT); // Set estado to 0 (inactive/deleted)
            $statement->bindValue(':id', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            throw new Exception("Database error while deleting PartidoBet: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the PartidoBet.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
            throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            // Validate valor_apostado
            $valorApostadoOriginal = $this->getValorApostado();
            if ($valorApostadoOriginal === null) {
                throw new Exception('Debe especificar el valor apostado');
            }

            $valorApostadoLimpio = format_numberclean((string)$valorApostadoOriginal);
            if (!is_numeric($valorApostadoLimpio)) {
                throw new Exception("El valor apostado proporcionado no es numérico después de la limpieza.");
            }
            if ((float)$valorApostadoLimpio <= 0) {
                throw new Exception("El valor apostado debe ser mayor que cero.");
            }
            $this->setValorApostado((float)$valorApostadoLimpio);

            // Validate cuota
            $cuotaOriginal = $this->getCuota();
            if ($cuotaOriginal === null) {
                throw new Exception('Debe especificar la cuota');
            }

            $cuotaLimpia = format_numberclean((string)$cuotaOriginal);
            if (!is_numeric($cuotaLimpia)) {
                throw new Exception("La cuota proporcionada no es numérica después de la limpieza.");
            }
            if ((float)$cuotaLimpia <= 0) {
                throw new Exception("La cuota debe ser mayor que cero.");
            }
            $this->setCuota((float)$cuotaLimpia);

            // Validate valor_recibido if set
            if ($this->getValorRecibido() !== null) {
                $valorRecibidoLimpio = format_numberclean((string)$this->getValorRecibido());
                if (!is_numeric($valorRecibidoLimpio)) {
                    throw new Exception("El valor recibido proporcionado no es numérico después de la limpieza.");
                }
                if ((float)$valorRecibidoLimpio < 0) {
                    throw new Exception("El valor recibido no puede ser negativo.");
                }
                $this->setValorRecibido((float)$valorRecibidoLimpio);
            }

            // Validate valor_profit if set
            if ($this->getValorProfit() !== null) {
                $valorProfitLimpio = format_numberclean((string)$this->getValorProfit());
                if (!is_numeric($valorProfitLimpio)) {
                    throw new Exception("El valor profit proporcionado no es numérico después de la limpieza.");
                }
                $this->setValorProfit((float)$valorProfitLimpio);
            }

            // Validate boolean fields
            if ($this->getEsCerrado() !== null && !in_array($this->getEsCerrado(), [0, 1], true)) {
                throw new Exception("El campo es_cerrado no es válido (debe ser 0 o 1).");
            }

            if ($this->getEsGanado() !== null && !in_array($this->getEsGanado(), [0, 1], true)) {
                throw new Exception("El campo es_ganado no es válido (debe ser 0 o 1).");
            }

            if ($this->getEstado() !== null && !in_array($this->getEstado(), [0, 1], true)) {
                throw new Exception("El estado no es válido (debe ser 0 o 1).");
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }

    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getFechaCreado(): ?string
    {
        return $this->fecha_creado;
    }

    public function setFechaCreado(?string $fecha_creado): self
    {
        $this->fecha_creado = $fecha_creado;
        return $this;
    }

    public function getValorApostado(): ?float
    {
        return $this->valor_apostado;
    }

    public function setValorApostado(?float $valor_apostado): self
    {
        $this->valor_apostado = $valor_apostado;
        return $this;
    }

    public function getCuota(): ?float
    {
        return $this->cuota;
    }

    public function setCuota(?float $cuota): self
    {
        $this->cuota = $cuota;
        return $this;
    }

    public function getEsCerrado(): ?int
    {
        return $this->es_cerrado;
    }

    public function setEsCerrado(?int $es_cerrado): self
    {
        $this->es_cerrado = $es_cerrado;
        return $this;
    }

    public function getEsGanado(): ?int
    {
        return $this->es_ganado;
    }

    public function setEsGanado(?int $es_ganado): self
    {
        $this->es_ganado = $es_ganado;
        return $this;
    }

    public function getValorRecibido(): ?float
    {
        return $this->valor_recibido;
    }

    public function setValorRecibido(?float $valor_recibido): self
    {
        $this->valor_recibido = $valor_recibido;
        return $this;
    }

    public function getValorProfit(): ?float
    {
        return $this->valor_profit;
    }

    public function setValorProfit(?float $valor_profit): self
    {
        $this->valor_profit = $valor_profit;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        $this->estado = $estado;
        return $this;
    }

    // --- CALCULATED METHODS ---

    /**
     * Calculates the valor_recibido based on business logic.
     * If es_cerrado=1 AND es_ganado=1, then valor_apostado × cuota, else 0
     *
     * @return float The calculated received amount
     */
    public function calcularValorRecibido(): float
    {
        if ($this->getEsCerrado() === 1 && $this->getEsGanado() === 1) {
            $valorApostado = $this->getValorApostado() ?? 0.0;
            $cuota = $this->getCuota() ?? 0.0;
            return $valorApostado * $cuota;
        }
        return 0.0;
    }

    /**
     * Calculates the valor_profit based on business logic.
     * valor_profit = valor_recibido - valor_apostado
     *
     * @return float The calculated profit amount
     */
    public function calcularValorProfit(): float
    {
        $valorRecibido = $this->calcularValorRecibido();
        $valorApostado = $this->getValorApostado() ?? 0.0;
        return $valorRecibido - $valorApostado;
    }

    /**
     * Gets the calculated valor_recibido (overrides stored value with calculated one).
     *
     * @return float The calculated received amount
     */
    public function getValorRecibidoCalculado(): float
    {
        return $this->calcularValorRecibido();
    }

    /**
     * Gets the calculated valor_profit (overrides stored value with calculated one).
     *
     * @return float The calculated profit amount
     */
    public function getValorProfitCalculado(): float
    {
        return $this->calcularValorProfit();
    }

    /**
     * Closes a bet by setting the appropriate values based on the result (won/lost).
     *
     * @param string $id The 'desordenado' string ID of the PartidoBet to close.
     * @param bool $isWon True if the bet was won, false if lost.
     * @param PDO $conexion The database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If the bet is not found, already closed, or database error occurs.
     * @throws InvalidArgumentException If the provided ID is invalid.
     */
    public static function closeBet(string $id, bool $isWon, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid bet ID provided for closing.");
        }

        try {
            // Retrieve the bet
            $bet = self::get($id, $conexion);
            if ($bet === null) {
                throw new Exception("Bet not found with ID: " . $id);
            }

            // Check if bet is already closed
            if ($bet->getEsCerrado() === 1) {
                throw new Exception("Bet is already closed.");
            }

            // Set closing values
            $bet->setEsCerrado(1);
            $bet->setEsGanado($isWon ? 1 : 0);

            if ($isWon) {
                // Calculate received amount: valor_apostado * cuota
                $valorRecibido = ($bet->getValorApostado() ?? 0.0) * ($bet->getCuota() ?? 0.0);
                $bet->setValorRecibido($valorRecibido);

                // Calculate profit: valor_recibido - valor_apostado
                $valorProfit = $valorRecibido - ($bet->getValorApostado() ?? 0.0);
                $bet->setValorProfit($valorProfit);
            } else {
                // Lost bet: no money received
                $bet->setValorRecibido(0.0);

                // Calculate profit (will be negative): 0 - valor_apostado
                $valorProfit = 0.0 - ($bet->getValorApostado() ?? 0.0);
                $bet->setValorProfit($valorProfit);
            }

            // Save the updated bet
            return $bet->guardar($conexion);

        } catch (Exception $e) {
            throw new Exception("Error closing bet: " . $e->getMessage());
        }
    }
}
