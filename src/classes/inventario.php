<?php

require_once __ROOT__ . '/src/classes/inventariocategoria.php';

class Inventario
{
    public string $id;
    public InventarioCategoria $inventariocategoria;
    public string $nombre;
    public int $estadoqty;
    public int $estado;
    public array $listado;
    private string $bd_table = 'inventario';
    private string $bd_alias = 'inv';
    private string $bd_id = 'id_inventario';
    private string $bd_idinventariocategoria = 'id_inventario_categoria';
    private string $bd_nombre = 'nombre';
    private string $bd_estadoqty = 'estado_qty';
    private string $bd_estado = 'estado';

    function __construct()
    {
        $this->id = '';
        $this->inventariocategoria = new InventarioCategoria();
        $this->nombre = '';
        $this->estadoqty = 0;
        $this->estado = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->inventariocategoria = new InventarioCategoria();
            $objeto->inventariocategoria->id = desordena($resultado[$cq->bd_idinventariocategoria]);

            $objeto->nombre = $resultado[$cq->bd_nombre];
            $objeto->estadoqty = $resultado[$cq->bd_estadoqty];
            $objeto->estado = $resultado[$cq->bd_estado];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($id, $conexion): self
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getList($idinventariocategoria, $conexion): array
    {
        try {
            $cq = new self;

            $query = "SELECT ";
            $query .= "  $cq->bd_alias.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "  AND $cq->bd_idinventariocategoria = :$cq->bd_idinventariocategoria ";
            $query .= "ORDER BY ";
            $query .= "  $cq->bd_nombre ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 1);
            $statement->bindValue(":$cq->bd_idinventariocategoria", ordena($idinventariocategoria));
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function getListAll($conexion): array
    {
        try {
            $categorias = InventarioCategoria::getList($conexion);
            $inventario = array();

            /** @var InventarioCategoria $categorias */
            foreach ($categorias as $categoria) {
                $newinventario = new self;
                $newinventario->inventariocategoria = $categoria;
                $newinventario->listado = self::getList($categoria->id, $conexion);

                $inventario[] = $newinventario;
            }

            return $inventario;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add($conexion): void
    {
        try {
            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_idinventariocategoria ";
            $query .= "  ,$cq->bd_nombre ";
            $query .= "  ,$cq->bd_estadoqty ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_idinventariocategoria ";
            $query .= "  ,:$cq->bd_nombre ";
            $query .= "  ,:$cq->bd_estadoqty ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idinventariocategoria", ordena($this->inventariocategoria->id));
            $statement->bindValue(":$cq->bd_nombre", strtoupper($this->nombre));
            $statement->bindValue(":$cq->bd_estadoqty", $this->estadoqty);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function modify($conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_idinventariocategoria = :$cq->bd_idinventariocategoria ";
            $query .= "  ,$cq->bd_nombre = :$cq->bd_nombre ";
            $query .= "  ,$cq->bd_estadoqty = :$cq->bd_estadoqty ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_idinventariocategoria", ordena($this->inventariocategoria->id));
            $statement->bindValue(":$cq->bd_nombre", strtoupper($this->nombre));
            $statement->bindValue(":$cq->bd_estadoqty", $this->estadoqty);
            $statement->bindValue(":$cq->bd_id", ordena($this->id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($id, $conexion): void
    {
        try {
            $cq = new self;

            $query = " UPDATE $cq->bd_table SET ";
            $query .= "  $cq->bd_estado = :$cq->bd_estado ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id = :$cq->bd_id ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_estado", 0);
            $statement->bindValue(":$cq->bd_id", ordena($id));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>