<?php

declare(strict_types=1);

namespace App\classes;

use InvalidArgumentException;
use Exception;
use PDO;
use PDOException;

/**
 * Represents a loan (Prestamo).
 * Assumes global functions ordena(), desordena(), validar_textovacio(), format_numberclean() are available.
 */
class Prestamo
{
    // --- Attributes ---
    private ?string $id     = null; // Stores the 'desordenado' ID
    private ?string $nombre = null;
    private ?float  $valor  = null;
    private ?int    $estado = null; // 0 = Inactivo, 1 = Activo
    private ?string $fecha_creacion = null; // Stores creation timestamp 'YYYY-MM-DD HH:MM:SS'
    private ?string $nota_adicional = null;
    private ?float  $valor_inicial = null; // Stores the initial value at creation
    private ?string $tipo = null; // Stores the loan type: "Credito" or "Debito"
    private ?int    $resuelto = null; // 0 = Unresolved, 1 = Resolved

    /**
     * Constructor: Initializes properties with default null values.
     */
    public function __construct()
    {
        $this->id     = null;
        $this->nombre = null;
        $this->valor  = null;
        $this->estado = null; // Default state might be considered active (1) upon creation, adjust if needed
        $this->fecha_creacion = null;
        $this->nota_adicional = null;
        $this->valor_inicial = null;
        $this->tipo = null;
        $this->resuelto = null; // Default to null, will be set to 0 on creation
    }

    /**
     * Static factory method to create an instance from an array (e.g., DB result).
     * Assumes $data contains raw column names from the database.
     *
     * @param array $data Associative array containing property values.
     * @return self Returns a new instance of Prestamo.
     * @throws Exception If 'desordena' function is missing or data is invalid.
     */
    public static function construct(array $data): self
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        try {
            $objeto = new self();
            // The ID from the DB ('id_prestamo') is assumed to be the 'ordenado' integer ID.
            $dbId = $data['id_prestamo'] ?? null;
            if ($dbId !== null) {
                $desordenadoId = desordena((string)$dbId);
                // Add check: Ensure desordena returned a valid, non-empty string ID
                if ($desordenadoId === null || trim((string)$desordenadoId) === '') {
                     error_log("desordena() returned empty/null for DB ID: " . $dbId);
                     throw new Exception("Error processing Prestamo ID during object construction. desordena() failed.");
                }
                $objeto->id = $desordenadoId; // Store the validated desordenado ID
            } else {
                $objeto->id = null; // No ID in data, likely a new object scenario before insert
            }
            // We store the 'desordenado' string ID internally.
            $objeto->nombre = $data['nombre'] ?? null;
            $objeto->valor  = isset($data['valor']) ? (float)$data['valor'] : null;
            $objeto->estado = isset($data['estado']) ? (int)$data['estado'] : null;
            $objeto->fecha_creacion = $data['fecha_creacion'] ?? null; // Assumes DB column exists
            $objeto->nota_adicional = $data['nota_adicional'] ?? null; // Assumes DB column exists
            $objeto->valor_inicial = isset($data['valor_inicial']) ? (float)$data['valor_inicial'] : null; // Assumes DB column exists
            $objeto->tipo = $data['tipo'] ?? null; // Assumes DB column 'tipo' exists
            $objeto->resuelto = isset($data['resuelto']) ? (int)$data['resuelto'] : null; // Assumes DB column 'resuelto' exists

            return $objeto;

        } catch (Exception $e) {
            // Consider logging the error here
            error_log("Error constructing Prestamo from data: " . print_r($data, true) . " Error: " . $e->getMessage());
            throw new Exception("Error constructing Prestamo: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a Prestamo object from the database by its 'desordenado' string ID.
     *
     * @param string $id       The 'desordenado' string ID of the Prestamo to retrieve.
     * @param PDO    $conexion The database connection object.
     * @return self|null A Prestamo object if found, null otherwise.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the provided ID is empty or invalid.
     */
    public static function get(string $id, PDO $conexion): ?self
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert the 'desordenado' string ID to the 'ordenado' integer ID for the query
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID.");
            }

            $query = <<<SQL
            SELECT *
            FROM prestamos
            WHERE id_prestamo = :id_prestamo
            LIMIT 1
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(":id_prestamo", $idOrdenado, PDO::PARAM_INT);
            $statement->execute();
            $resultado = $statement->fetch(PDO::FETCH_ASSOC);

            return $resultado ? static::construct($resultado) : null;

        } catch (PDOException $e) {
            error_log("Database error getting Prestamo (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error fetching Prestamo: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena or construct
            error_log("Error getting Prestamo (ID: $id): " . $e->getMessage());
            throw new Exception("Error fetching Prestamo: " . $e->getMessage());
        }
    }

    /**
     * Retrieves a list of all active Prestamo objects.
     *
     * @param PDO $conexion The database connection object.
     * @return array An array of Prestamo objects. Returns an empty array if no results.
     * @throws Exception If there is an error during the database query or object construction.
     */
    public static function getList(PDO $conexion): array
    {
        try {
            $query = <<<SQL
            SELECT *
            FROM prestamos
            WHERE estado = :estado AND resuelto = :resuelto
            ORDER BY nombre
            SQL;

            $statement = $conexion->prepare($query);
            // Assuming 1 means active and 0 means unresolved
            $statement->bindValue(":estado", 1, PDO::PARAM_INT);
            $statement->bindValue(":resuelto", 0, PDO::PARAM_INT);
            $statement->execute();
            $resultados = $statement->fetchAll(PDO::FETCH_ASSOC);

            $listado = [];
            if ($resultados) {
                foreach ($resultados as $resultado) {
                    try {
                        $listado[] = static::construct($resultado);
                    } catch (Exception $e) {
                        error_log("Error constructing Prestamo during getList for data: " . print_r($resultado, true) . " Error: " . $e->getMessage());
                        // Decide whether to skip this item or re-throw
                        // For now, skip and log
                    }
                }
            }
            return $listado;

        } catch (PDOException $e) {
            error_log("Database error getting Prestamo list: " . $e->getMessage());
            throw new Exception("Database error fetching Prestamo list: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from construct
            error_log("Error getting Prestamo list: " . $e->getMessage());
            throw new Exception("Error fetching Prestamo list: " . $e->getMessage());
        }
    }

    /**
     * Calculates the sum of 'valor' for an array of Prestamo objects.
     *
     * @param Prestamo[] $prestamos An array of Prestamo objects.
     * @return float The total sum of 'valor'.
     */
    public static function getSumValor(array $prestamos): float
    {
        $valorTotal = 0.0;
        foreach ($prestamos as $prestamo) {
            // Use getter to access private property
            if ($prestamo instanceof self && $prestamo->getValor() !== null) {
                $valorTotal += $prestamo->getValor();
            }
        }
        return $valorTotal;
    }

    /**
     * Calculates the sum of 'valor' for an array of Prestamo objects, separated by type.
     *
     * @param Prestamo[] $prestamos An array of Prestamo objects.
     * @return array An associative array with 'Credito' and 'Debito' totals. ['Credito' => float, 'Debito' => float]
     */
    public static function getSumValorByType(array $prestamos): array
    {
        $totals = ['Credito' => 0.0, 'Debito' => 0.0];
        foreach ($prestamos as $prestamo) {
            if ($prestamo instanceof self && $prestamo->getValor() !== null) {
                $tipo = $prestamo->getTipo();
                if ($tipo === 'Credito') {
                    $totals['Credito'] += $prestamo->getValor();
                } elseif ($tipo === 'Debito') {
                    $totals['Debito'] += $prestamo->getValor();
                }
            }
        }
        return $totals;
    }


    /**
     * Saves (inserts or updates) the current Prestamo instance to the database.
     * Sets the 'estado' to 1 (active) implicitly during insert/update if not set.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If validation fails or a database error occurs.
     */
    public function guardar(PDO $conexion): bool
    {
        // Debugging: Log the ID right before determining the operation type
        $currentId = $this->getId();
        error_log("Prestamo::guardar() called. ID is: " . var_export($currentId, true));

        // Determine if it's an insert or update *before* validation
        $isInsertOperation = ($currentId === null || empty(trim($currentId)));
        error_log("Prestamo::guardar() - isInsertOperation determined as: " . var_export($isInsertOperation, true));


        // Call validation, passing the correct flag
        $this->validarDatos($isInsertOperation); // Pass true for insert, false for update

        try {
            // Use the pre-determined flag for the operation
            // Remove redundant ID checks here, rely on _update's internal checks
            if (!$isInsertOperation) { // It's an update
                return $this->_update($conexion);
            } else { // It's an insert
                return $this->_insert($conexion);
            }
        } catch (PDOException $e) {
            $idInfo = $this->getId() ?? 'N/A';
            error_log("Database error saving Prestamo (ID: {$idInfo}): " . $e->getMessage());
            // Could check for specific integrity constraint violations if needed
            throw new Exception("Database error saving Prestamo: " . $e->getMessage());
        } catch (Exception $e) {
            error_log("General error saving Prestamo: " . $e->getMessage());
            throw new Exception("Error saving Prestamo: " . $e->getMessage());
        }
    }

    /**
     * Inserts the current Prestamo instance into the database. (Private method)
     * Sets estado to 1 (active) by default on insert.
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'desordena' function is missing.
     */
    private function _insert(PDO $conexion): bool
    {
        if (!function_exists('desordena')) {
            throw new Exception("Global function 'desordena' is required but not found.");
        }

        // Assume new prestamos are active by default
        $estadoParaGuardar = $this->getEstado() ?? 1;

        $query = <<<SQL
        INSERT INTO prestamos (
            nombre,
            valor,
            estado,
            fecha_creacion,
            nota_adicional,
            valor_inicial,
            tipo,
            resuelto
        ) VALUES (
            :nombre,
            :valor,
            :estado,
            :fecha_creacion,
            :nota_adicional,
            :valor_inicial,
            :tipo,
            :resuelto
        )
        SQL;

        $statement = $conexion->prepare($query);

        // Use manual fecha_creacion if set, otherwise use current date
        $fechaCreacionActual = $this->getFechaCreacion() ?? date('Y-m-d');
        $valorInicialActual = $this->getValor(); // Set initial value to current value on insert

        // Set resuelto to 0 (unresolved) by default for new loans
        $resueltoParaGuardar = $this->getResuelto() ?? 0;

        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR); // PDO often handles float as string
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':fecha_creacion', $fechaCreacionActual, PDO::PARAM_STR);
        $statement->bindValue(':nota_adicional', $this->getNotaAdicional(), PDO::PARAM_STR);
        $statement->bindValue(':valor_inicial', $valorInicialActual, PDO::PARAM_STR);
        $statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR); // Bind the new tipo
        $statement->bindValue(':resuelto', $resueltoParaGuardar, PDO::PARAM_INT);

        $success = $statement->execute();

        if ($success) {
            // Get the last inserted ID (which is the 'ordenado' integer ID)
            $lastIdOrdenado = $conexion->lastInsertId();
            if ($lastIdOrdenado) {
                // Convert it to 'desordenado' string ID and set it on the object
                $this->setId(desordena((string)$lastIdOrdenado));
                // Also update the object properties with values actually inserted
                $this->setEstado($estadoParaGuardar);
                $this->setFechaCreacion($fechaCreacionActual);
                // Nota adicional is already set via setter before save
                $this->setValorInicial($valorInicialActual);
            } else {
                 error_log("Failed to retrieve lastInsertId after Prestamo insert.");
                 // Technically succeeded but couldn't get ID back, might indicate issues
                 return false;
            }
        } else {
            error_log("Failed to insert Prestamo: " . implode(" | ", $statement->errorInfo()));
        }

        return $success;
    }

    /**
     * Updates the current Prestamo instance in the database. (Private method)
     *
     * @param PDO $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws PDOException On database error.
     * @throws Exception If 'ordena' function is missing or ID is invalid.
     */
    private function _update(PDO $conexion): bool
    {
        if ($this->getId() === null || empty(trim($this->getId()))) {
            throw new Exception("Cannot update Prestamo without a valid ID.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        // Convert internal 'desordenado' string ID to 'ordenado' integer ID for WHERE clause
        $idOrdenado = ordena($this->getId());
         if ($idOrdenado === false || $idOrdenado <= 0) {
             throw new Exception("Failed to process the Prestamo ID for update: " . $this->getId());
         }

        // Use current estado if set, otherwise default to 1 (active)
        $estadoParaGuardar = $this->getEstado() ?? 1;

        $query = <<<SQL
        UPDATE prestamos SET
             nombre = :nombre
            ,valor = :valor
            ,estado = :estado
            ,nota_adicional = :nota_adicional
            ,fecha_creacion = :fecha_creacion
            ,tipo = :tipo
            ,resuelto = :resuelto
        WHERE id_prestamo = :id_prestamo
        SQL;

        $statement = $conexion->prepare($query);

        $statement->bindValue(':nombre', $this->getNombre(), PDO::PARAM_STR);
        $statement->bindValue(':valor', $this->getValor(), PDO::PARAM_STR);
        $statement->bindValue(':estado', $estadoParaGuardar, PDO::PARAM_INT);
        $statement->bindValue(':nota_adicional', $this->getNotaAdicional(), PDO::PARAM_STR);
        $statement->bindValue(':fecha_creacion', $this->getFechaCreacion(), PDO::PARAM_STR);
        $statement->bindValue(':tipo', $this->getTipo(), PDO::PARAM_STR); // Bind the new tipo
        $statement->bindValue(':resuelto', $this->getResuelto(), PDO::PARAM_INT); // Bind the resuelto field
        $statement->bindValue(':id_prestamo', $idOrdenado, PDO::PARAM_INT); // Bind the 'ordenado' ID

        $success = $statement->execute();

        if (!$success) {
            error_log("Failed to update Prestamo (ID: {$this->getId()} / {$idOrdenado}): " . implode(" | ", $statement->errorInfo()));
        } else {
             // Ensure object's estado reflects the saved state
             $this->setEstado($estadoParaGuardar);
        }

        return $success;
    }

    /**
     * Deletes (soft deletes) a Prestamo record by setting its estado to 0.
     *
     * @param string $id       The 'desordenado' string ID of the Prestamo to delete.
     * @param PDO    $conexion The PDO database connection object.
     * @return bool True on success, false on failure.
     * @throws Exception If 'ordena' function is missing or DB error occurs.
     * @throws InvalidArgumentException If the ID is invalid.
     */
    public static function delete(string $id, PDO $conexion): bool
    {
        if (empty(trim($id))) {
            throw new InvalidArgumentException("Invalid ID provided for deletion.");
        }
        if (!function_exists('ordena')) {
            throw new Exception("Global function 'ordena' is required but not found.");
        }

        try {
            // Convert 'desordenado' string ID to 'ordenado' integer ID
            $idOrdenado = ordena($id);
            if ($idOrdenado === false || $idOrdenado <= 0) {
                 throw new InvalidArgumentException("Failed to process the provided ID for deletion.");
            }

            $query = <<<SQL
            UPDATE prestamos
            SET estado = :estado
            WHERE id_prestamo = :id_prestamo
            SQL;

            $statement = $conexion->prepare($query);
            $statement->bindValue(':estado', 0, PDO::PARAM_INT); // Set estado to 0 for soft delete
            $statement->bindValue(':id_prestamo', $idOrdenado, PDO::PARAM_INT);

            return $statement->execute();

        } catch (PDOException $e) {
            error_log("Database error soft-deleting Prestamo (ID: $id / $idOrdenado): " . $e->getMessage());
            throw new Exception("Database error deleting Prestamo: " . $e->getMessage());
        } catch (Exception $e) { // Catch potential errors from ordena
             error_log("Error soft-deleting Prestamo (ID: $id): " . $e->getMessage());
             throw new Exception("Error deleting Prestamo: " . $e->getMessage());
        }
    }

    /**
     * Validates the essential data for the Prestamo.
     * Assumes global validation/formatting functions exist.
     * Throws an Exception if validation fails.
     *
     * @param bool $isInsert Optional flag to indicate if validation is for an insert operation.
     * @throws Exception If validation fails.
     */
    private function validarDatos(bool $isInsert = false): void // Add optional parameter
    {
        // Assume global functions exist and handle throwing exceptions on failure
        if (!function_exists('validar_textovacio') || !function_exists('format_numberclean')) {
             throw new Exception("Required global validation/formatting functions are missing.");
        }

        try {
            validar_textovacio($this->getNombre(), 'Debe especificar el nombre');

            // Validate Valor
            $valorOriginal = $this->getValor(); // Get the value currently set on the object
            if ($valorOriginal === null) {
                 // If the value is strictly null, it's missing.
                 throw new Exception('Debe especificar el valor');
            }

            // If not null, proceed to clean and validate numeric format
            $valorLimpio = format_numberclean((string)$valorOriginal);
            if (!is_numeric($valorLimpio)) {
                 // If after cleaning it's not numeric, it's invalid.
                 throw new Exception("El valor proporcionado no es numérico después de la limpieza.");
            }
            // Re-set the value on the object with the cleaned float value, allowing 0 and negative numbers.
            $this->setValor((float)$valorLimpio);

            // Validate valor_inicial ONLY during insert operations
            if ($isInsert) {
                // On insert, valor_inicial should be derived from valor, which we just validated.
                // It must be positive on creation.
                $valorInicialValidar = $this->getValor(); // Use the current (validated) valor for initial value check

                if ($valorInicialValidar === null) {
                     // This shouldn't happen if valor validation passed, but check defensively
                     throw new Exception('El valor inicial no puede ser nulo durante la creación.');
                }

                // Restore and enforce positivity check for the initial value *at creation time*
                if ($valorInicialValidar <= 0) {
                     throw new Exception("El valor inicial del préstamo debe ser mayor que cero.");
                }
                // No need to clean/check numeric again, as it comes from the already validated 'valor'
            }
            // During UPDATE (when $isInsert is false), we skip the valor_inicial positivity check.

            // Nota adicional doesn't need strict validation for now.
            // Fecha creacion is handled internally or comes from DB, basic format check could be added if needed.

            // Validate estado if it's set (allow null initially, but maybe enforce 0 or 1?)
            if ($this->getEstado() !== null && !in_array($this->getEstado(), [0, 1], true)) {
                 throw new Exception("El estado del préstamo no es válido (debe ser 0 o 1).");
            }

            // Validate tipo
            $tipo = $this->getTipo();
            if ($tipo === null || !in_array($tipo, ['Credito', 'Debito'], true)) {
                throw new Exception("El tipo de préstamo no es válido (debe ser 'Credito' o 'Debito').");
            }

        } catch (Exception $e) {
            // Re-throw validation exceptions to be caught by the calling method (e.g., guardar)
            throw new Exception("Validation failed: " . $e->getMessage());
        }
    }


    // --- GETTERS AND SETTERS ---

    public function getId(): ?string
    {
        return $this->id;
    }

    /**
     * Sets the 'desordenado' string ID.
     */
    public function setId(?string $id): self
    {
        $this->id = $id;
        return $this;
    }

    public function getNombre(): ?string
    {
        return $this->nombre;
    }

    public function setNombre(?string $nombre): self
    {
        $this->nombre = $nombre;
        return $this;
    }

    public function getValor(): ?float
    {
        return $this->valor;
    }

    public function setValor(?float $valor): self
    {
        $this->valor = $valor;
        return $this;
    }

    public function getEstado(): ?int
    {
        return $this->estado;
    }

    public function setEstado(?int $estado): self
    {
        // Optional: Add validation here too, e.g., ensure $estado is 0 or 1
        // if ($estado !== null && !in_array($estado, [0, 1])) {
        //     throw new InvalidArgumentException("Estado must be 0, 1, or null.");
        // }
        $this->estado = $estado;
        return $this;
    }

    // --- Getters and Setters for new attributes ---

    public function getFechaCreacion(): ?string
    {
        return $this->fecha_creacion;
    }

    /**
     * Sets the creation date string. Should generally not be set manually after creation.
     */
    public function setFechaCreacion(?string $fecha_creacion): self
    {
        // Optional: Add validation for date format if needed
        $this->fecha_creacion = $fecha_creacion;
        return $this;
    }

    public function getNotaAdicional(): ?string
    {
        return $this->nota_adicional;
    }

    public function setNotaAdicional(?string $nota_adicional): self
    {
        $this->nota_adicional = $nota_adicional;
        return $this;
    }

    public function getValorInicial(): ?float
    {
        return $this->valor_inicial;
    }

    /**
     * Sets the initial value. Should generally not be set manually after creation.
     */
    public function setValorInicial(?float $valor_inicial): self
    {
        $this->valor_inicial = $valor_inicial;
        return $this;
    }

    public function getTipo(): ?string
    {
        return $this->tipo;
    }

    /**
     * Sets the loan type.
     *
     * @param string|null $tipo Must be 'Credito', 'Debito', or null.
     * @return self
     * @throws InvalidArgumentException If the type is invalid.
     */
    public function setTipo(?string $tipo): self
    {
        if ($tipo !== null && !in_array($tipo, ['Credito', 'Debito'], true)) {
            throw new InvalidArgumentException("Tipo must be 'Credito', 'Debito', or null.");
        }
        $this->tipo = $tipo;
        return $this;
    }

    public function getResuelto(): ?int
    {
        return $this->resuelto;
    }

    /**
     * Sets the resolved status.
     *
     * @param int|null $resuelto Must be 0 (unresolved), 1 (resolved), or null.
     * @return self
     * @throws InvalidArgumentException If the value is invalid.
     */
    public function setResuelto(?int $resuelto): self
    {
        if ($resuelto !== null && !in_array($resuelto, [0, 1], true)) {
            throw new InvalidArgumentException("Resuelto must be 0, 1, or null.");
        }
        $this->resuelto = $resuelto;
        return $this;
    }
}
