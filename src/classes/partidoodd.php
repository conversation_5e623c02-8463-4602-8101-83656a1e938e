<?php

require_once __ROOT__ . '/src/classes/partido.php';
require_once __ROOT__ . '/src/classes/apuestatipo.php';

class PartidoOdd
{
    public string $id;
    public Partido $partido;
    public ApuestaTipo $apuesta_tipo;
    public float $valor_apuesta;
    public float $odd;
    private string $bd_table = 'partidos_odds';
    private string $bd_alias = 'parod';
    private string $bd_id = 'id_partido_odd';
    private string $bd_id_partido = 'id_partido';
    private string $bd_id_apuesta_tipo = 'id_apuesta_tipo';
    private string $bd_valor_apuesta = 'valor_apuesta';
    private string $bd_odd = 'odd';

    function __construct()
    {
        $this->id = '';
        $this->partido = new Partido();
        $this->partido->id = '';
        $this->apuesta_tipo = new ApuestaTipo();
        $this->apuesta_tipo->id = '';
        $this->valor_apuesta = 0;
        $this->odd = 0;
    }

    /**
     * @param $resultado
     * @return self
     * @throws Exception
     */
    public static function construct($resultado): self
    {
        try {
            $cq = new self;

            $objeto = new self;
            $objeto->id = desordena($resultado[$cq->bd_id]);
            $objeto->partido->id = desordena($resultado[$cq->bd_id_partido]);
            $objeto->apuesta_tipo->id = desordena($resultado[$cq->bd_id_apuesta_tipo]);
            $objeto->valor_apuesta =$resultado[$cq->bd_valor_apuesta];
            $objeto->odd = $resultado[$cq->bd_odd];

            return $objeto;

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get($paramref, $conexion): self
    {
        try {
            #region region parametros
            $idpartido       = (isset($paramref['idpartido'])) ? $paramref['idpartido'] : "";
            $id_apuesta_tipo = (isset($paramref['id_apuesta_tipo'])) ? $paramref['id_apuesta_tipo'] : "";
            $valor_apuesta   = (isset($paramref['valor_apuesta'])) ? $paramref['valor_apuesta'] : "";
            #endregion parametros

            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cqa ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id > 0 ";

            if(!empty($idpartido)){
                $query .= "AND $cqa.$cq->bd_id_partido = :$cq->bd_id_partido ";
            }
            if(!empty($id_apuesta_tipo)){
                $query .= "AND $cqa.$cq->bd_id_apuesta_tipo = :$cq->bd_id_apuesta_tipo ";
            }
            if(!empty($valor_apuesta)){
                $query .= "AND $cqa.$cq->bd_valor_apuesta = :$cq->bd_valor_apuesta ";
            }

            $statement = $conexion->prepare($query);

            #region region bindvalue
            if(!empty($idpartido)){
                $statement->bindValue(":$cq->bd_id_partido", ordena($idpartido));
            }
            if(!empty($id_apuesta_tipo)){
                $statement->bindValue(":$cq->bd_id_apuesta_tipo", ordena($id_apuesta_tipo));
            }
            if(!empty($valor_apuesta)){
                $statement->bindValue(":$cq->bd_valor_apuesta", $valor_apuesta);
            }
            #endregion bindvalue

            $statement->execute();
            $resultado = $statement->fetch();

            if ($resultado) {
                return self::construct($resultado);

            } else {
                return new self;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_odds(array $paramref, PDO $conexion): float|int
    {
        try {
            $odd = self::get($paramref, $conexion);

            if(empty($odd->id)){
                return 0;
            } else{
                return $odd->odd;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function get_list($conexion): array
    {
        try {
            $cq = new self;
            $cqa = $cq->bd_alias;

            $query = "SELECT ";
            $query .= "  $cqa.* ";
            $query .= "FROM $cq->bd_table $cq->bd_alias ";
            $query .= "WHERE ";

            $statement = $conexion->prepare($query);
            $statement->execute();
            $resultados = $statement->fetchAll();

            if (!$resultados) {
                return array();
            } else {
                $listado = array();

                foreach ($resultados as $resultado) {
                    $listado[] = self::construct($resultado);
                }

                return $listado;
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    function add(PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = "INSERT INTO $cq->bd_table (";
            $query .= "  $cq->bd_id_partido ";
            $query .= "  ,$cq->bd_id_apuesta_tipo ";
            $query .= "  ,$cq->bd_valor_apuesta ";
            $query .= "  ,$cq->bd_odd ";
            $query .= ") VALUES (";
            $query .= "  :$cq->bd_id_partido ";
            $query .= "  ,:$cq->bd_id_apuesta_tipo ";
            $query .= "  ,:$cq->bd_valor_apuesta ";
            $query .= "  ,:$cq->bd_odd ";
            $query .= ") ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_partido", ordena($this->partido->id));
            $statement->bindValue(":$cq->bd_id_apuesta_tipo", ordena($this->apuesta_tipo->id));
            $statement->bindValue(":$cq->bd_valor_apuesta", $this->valor_apuesta);
            $statement->bindValue(":$cq->bd_odd", $this->odd);
            $statement->execute();

            $this->id = desordena($conexion->lastInsertId());

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function add_multiple($idpartido,array $odds, PDO $conexion): void{
        try {
            self::delete($idpartido,$conexion);

            /** @var self[] $odds */
            foreach ($odds as $odd) {
                $odd->add($conexion);
            }
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }

    /**
     * @throws Exception
     */
    public static function delete($idpartido,PDO $conexion): void
    {
        try {
            $cq = new self;

            $query = " DELETE FROM $cq->bd_table ";
            $query .= "WHERE ";
            $query .= "  $cq->bd_id_partido = :$cq->bd_id_partido ";

            $statement = $conexion->prepare($query);
            $statement->bindValue(":$cq->bd_id_partido", ordena($idpartido));
            $statement->execute();

        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
    }
}

?>