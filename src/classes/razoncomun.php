<?php

class RazonComun
{
	public string $id;
	public string $nombre;
	public int $estado;
	private string $bd_table  = 'razones_comunes';
	private string $bd_alias  = 'razcom';
	private string $bd_id     = 'id';
	private string $bd_nombre = 'nombre';
	private string $bd_estado = 'estado';
	
	function __construct()
	{
		$this->id     = '';
		$this->nombre = '';
		$this->estado = 0;
	}
	
	/**
	 * @param $resultado
	 * @return self
	 * @throws Exception
	 */
	public static function construct($resultado): self
	{
		try {
			$cq = new self;
			
			$objeto = new self;
			$objeto->id     = desordena($resultado[$cq->bd_id]);
			$objeto->nombre = $resultado[$cq->bd_nombre];
			$objeto->estado = $resultado[$cq->bd_estado];
			
			return $objeto;
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get($id, PDO $conexion): self
	{
		try {
			$cq = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cqa ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			$resultado = $statement->fetch();
			
			if ($resultado) {
				return self::construct($resultado);
				
			} else {
				return new self;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function get_list(array $paramref, PDO $conexion): array
	{
		try {
			$cq = new self;
			$cqa = $cq->bd_alias;
			
			$query = "SELECT ";
			$query .= "  $cqa.* ";
			$query .= "FROM $cq->bd_table $cq->bd_alias ";
			$query .= "WHERE ";
			$query .= "  $cqa.$cq->bd_estado = 1 ";
			$query .= "ORDER BY ";
			$query .= "  $cqa.$cq->bd_nombre ";
			
			$statement = $conexion->prepare($query);
			$statement->execute();
			$resultados = $statement->fetchAll();
			
			if (!$resultados) {
				return array();
			} else {
				$listado = array();
				
				foreach ($resultados as $resultado) {
					$listado[] = self::construct($resultado);
				}
				
				return $listado;
			}
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	function agregar(PDO $conexion): void
	{
		try {
			$this->validar_data();
			
			$cq = new self;
			
			$query = "INSERT INTO $cq->bd_table (";
			$query .= "  $cq->bd_nombre ";
			$query .= ") VALUES (";
			$query .= "  :$cq->bd_nombre ";
			$query .= ") ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_nombre", $this->nombre);
			$statement->execute();
			
			$this->id = desordena($conexion->lastInsertId());
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	public static function eliminar($id, PDO $conexion): void
	{
		try {
			$cq = new self;
			
			$query = " UPDATE $cq->bd_table SET ";
			$query .= "  $cq->bd_estado = 0 ";
			$query .= "WHERE ";
			$query .= "  $cq->bd_id = :$cq->bd_id ";
			
			$statement = $conexion->prepare($query);
			$statement->bindValue(":$cq->bd_id", ordena($id));
			$statement->execute();
			
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
	
	/**
	 * @throws Exception
	 */
	private function validar_data(): void
	{
		try {
			validar_campovacio($this->nombre, 'Debe especificar el nombre');
		
		} catch (Exception $e) {
			throw new Exception($e->getMessage());
		}
	}
}

?>