<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/product.php';
require_once __ROOT__ . '/src/general/preparar.php';

if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_add'])) {
    try {
        $title = limpiar_datos($_POST['title']);
        $brand = limpiar_datos($_POST['brand']);
        $qty = limpiar_datos($_POST['qty']);

        validar_textovacio($title, 'Specify title.');
        validar_textovacio($brand, 'Specify brand.');
        validar_textovacionotzero($qty, 'Specify qty.');

        $product = new Product;
        $product->title = $title;
        $product->brand = $brand;
        $product->qty = $qty;
        $product->add($conexion);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}

$products = Product::get_list($conexion);

require_once __ROOT__ . '/views/aproducts.view.php';

?>