<?php session_start();

global $conexion;

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/stockposiciontransaccion.php';
require_once __ROOT__ . '/src/general/preparar.php';

$transacciones  = array();
$total_realized = 0;
#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion get
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $mes = limpiar_datos($_POST['mes']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text    = $e->getMessage();
    }
}
#endregion postsolo
#region try
try {
    if(!empty($mes)){
        $transacciones  = StockPosicionTransaccion::get_list(array('mes' => $mes), $conexion);
        $total_realized = array_sum(array_column($transacciones, 'realized'));
    }
} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion try

require_once __ROOT__ . '/views/lstocks_reporte.view.php';

?>