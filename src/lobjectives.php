<?php session_start();

require_once '../config/config.php';
require_once __ROOT__ . '/src/query/conexion.php';
require_once __ROOT__ . '/src/sessions/sessions.php';
require_once __ROOT__ . '/src/general/general.php';
require_once __ROOT__ . '/src/general/validaciones.php';
require_once __ROOT__ . '/src/classes/usuario.php';
require_once __ROOT__ . '/src/classes/objective.php';
require_once __ROOT__ . '/src/general/preparar.php';

$objectives = array();
$bosses = array();

#region get
if ($_SERVER['REQUEST_METHOD'] == 'GET') {
    try {
        if (isset($_SESSION['selectedtipo'])) {
            $selectedtipo = $_SESSION['selectedtipo'];
            $selectedboss = $_SESSION['selectedboss'];

            unset($_SESSION['selectedtipo']);
            unset($_SESSION['selectedboss']);
        } else {
            header('Location: lgroupobjectives');
        }
    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region postsolo
if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    try {
        $selectedtipo = limpiar_datos($_POST['selectedtipo']);
        $selectedboss = limpiar_datos($_POST['selectedboss']);

    } catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_doneobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_doneobjective'])) {
    try {
        $conexion->beginTransaction();

        $modobjective = new Objective;
        $modobjective->id_objective = limpiar_datos($_POST['mdldoneobjective_id_objective']);
        $modobjective->modify_done($conexion);

        $conexion->commit();

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_pinobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_pinobjective'])) {
    try {
        $conexion->beginTransaction();

        $modobjective = new Objective;
        $modobjective->id_objective = limpiar_datos($_POST['mdlpinobjective_id_objective']);
        $modobjective->modify_pinned($conexion);

        $conexion->commit();

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_unpinobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_unpinobjective'])) {
    try {
        $conexion->beginTransaction();

        $modobjective = new Objective;
        $modobjective->id_objective = limpiar_datos($_POST['mdlunpinobjective_id_objective']);
        $modobjective->modify_unpinned($conexion);

        $conexion->commit();

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_delobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_delobjective'])) {
    try {
        $conexion->beginTransaction();

        $modobjective = new Objective;
        $modobjective->id_objective = limpiar_datos($_POST['mdldelobjective_id_objective']);
        $modobjective->delete($conexion);

        $conexion->commit();

    } catch (Exception $e) {
        $conexion->rollback();

        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_editobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_editobjective'])) {
    try {
        $id_objective = limpiar_datos($_POST['selectedobjective']);

        $_SESSION['id_objective'] = $id_objective;
        $_SESSION['selectedtipo'] = $selectedtipo;
        $_SESSION['selectedboss'] = $selectedboss;

        header('Location: eobjective');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region sub_addobjective
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['sub_addobjective'])) {
    try {
        $_SESSION['selectedtipo'] = $selectedtipo;
        $_SESSION['selectedboss'] = $selectedboss;

        header('Location: iobjective');

	} catch (Exception $e) {
        $error_display = 'show';
        $error_text = $e->getMessage();
    }
}
#endregion
#region try
try {
    $objectivespinned = Objective::get_list(1, $selectedtipo, $selectedboss, '', $conexion);
    $objectiveshigh = Objective::get_list(0, $selectedtipo, $selectedboss, Objective::PRIORIDAD_HIGH, $conexion);
    $objectivesmedium = Objective::get_list(0, $selectedtipo, $selectedboss, Objective::PRIORIDAD_MEDIUM, $conexion);
    $objectiveslow = Objective::get_list(0, $selectedtipo, $selectedboss, Objective::PRIORIDAD_LOW, $conexion);
    $objectivesnone = Objective::get_list(0, $selectedtipo, $selectedboss, Objective::PRIORIDAD_NONE, $conexion);

    $objective = array();
    $objective['objectives'] = $objectivespinned;
    $objective['priority'] = 'Pinned';
    $objectives[] = $objective;
    $objective = array();
    $objective['objectives'] = $objectiveshigh;
    $objective['priority'] = 'High';
    $objectives[] = $objective;
    $objective = array();
    $objective['objectives'] = $objectivesmedium;
    $objective['priority'] = 'Medium';
    $objectives[] = $objective;
    $objective = array();
    $objective['objectives'] = $objectiveslow;
    $objective['priority'] = 'Low';
    $objectives[] = $objective;
    $objective = array();
    $objective['objectives'] = $objectivesnone;
    $objective['priority'] = 'None';
    $objectives[] = $objective;

    switch ($selectedtipo) {
        case Objective::TIPO_SOLICITANTE:
            $bosses = Objective::get_listgroupbysolicitante($selectedboss, $conexion);

            break;
        case Objective::TIPO_PROYECTO:
            $bosses = Objective::get_listgroupbyproyecto($selectedboss, $conexion);

            break;
    }

} catch (Exception $e) {
    $error_display = 'show';
    $error_text = $e->getMessage();
}
#endregion

require_once __ROOT__ . '/views/lobjectives.view.php';

?>

