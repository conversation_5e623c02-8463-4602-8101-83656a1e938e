<!DOCTYPE html>
<html lang="en"  class="dark-mode">
<head>
	<meta charset="utf-8" />
	<title>My Dash | Work</title>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport" />
	<meta content="" name="description" />
	<meta content="" name="author" />
	
	<!-- #head -->
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
</head>
<body>
	<!-- BEGIN #loader -->
	<div id="loader" class="app-loader">
		<span class="spinner"></span>
	</div>
	<!-- END #loader -->

	<!-- BEGIN #app -->
	<div id="app" class="app app-header-fixed app-sidebar-fixed">
		<!-- #header -->
		<?php require_once __ROOT__ . '/views/header.view.php'; ?>

		<!-- #sidebar -->
		<?php require_once __ROOT__ . '/views/sidebar.view.php'; ?>

		<!-- BEGIN #content -->
		<div id="content" class="app-content">
			<?php require_once __ROOT__ . '/views/labels.view.php'; ?>
			
			<!-- BEGIN page-header -->
			<h1 class="page-header">Work</h1>
			<!-- END page-header -->
			
			<form action="atasks" method="POST">
				<!-- BEGIN row -->
				<div class="row">
					<div class="col-1">
						<a class="btn btn-primary btn-inline w-100" href="#mdl_createproject" data-bs-toggle="modal">
							<i class="fa fa-plus fa-lg fa-fw"></i>
						</a>
					</div>
					<div class="col-1">
						<button type="submit" id="sub_search" name="sub_search" class="btn btn-success btn-inline w-100">
							<i class="fa fa-magnifying-glass fa-lg fa-fw"></i>
						</button>
					</div>
					<div class="col">
						<div class="form-floating">
							<select id="id_project" name="id_project" class="form-select">
								<option value="">--</option>
								
								<?php foreach ($projects as $project): ?>
									<option <?php @recover_var_list($id_project, $project->id_project) ?> value="<?php echo limpiar_datos($project->id_project); ?>">
										<?php echo limpiar_datos($project->name); ?>
									</option>
								<?php endforeach; ?>
							</select>
							<label for="id_project" class="d-flex align-items-center fs-15px">
								Project:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->

				<hr/>

				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px" autofocus name="name" id="name" value="<?php echo @recover_var($name) ?>"/>
							<label for="name" class="d-flex align-items-center fs-15px">
								Task:
							</label>
						</div>
					</div>
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px text-uppercase" name="module" id="module" value="<?php echo @recover_var($module) ?>"/>
							<label for="module" class="d-flex align-items-center fs-15px">
								Module:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->

				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<div class="form-floating">
							<input type="text" class="form-control fs-15px" name="note" id="note" value="<?php echo @recover_var($note) ?>"/>
							<label for="note" class="d-flex align-items-center fs-15px">
								Note:
							</label>
						</div>
					</div>
				</div>
				<!-- END row -->

				<!-- BEGIN row -->
				<div class="row mt-3">
					<div class="col">
						<button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
							<i class="fa fa-arrow-down fa-lg fa-fw"></i>
						</button>
					</div>
					<div class="col-2">
						<a href="aworklog" class="btn btn-primary w-100">
							<i class="fa fa-book-open fa-lg fa-fw"></i>
						</a>
					</div>
				</div>
				<!-- END row -->

				<!-- BEGIN panel -->
				<div class="panel panel-inverse mt-3">
					<div class="panel-heading">
						<h4 class="panel-title">Tasks</h4>
					</div>
					<!-- BEGIN panel-body -->
					<div class="panel-body" style="height: 350px;overflow: auto">
						<table class="table table-hover table-sm">
							<thead>
								<tr>
									<th></th>
									<th class="text-start">Name</th>
									<th class="text-start">Module</th>
								</tr>
							</thead>
							<tbody class="fs-14px">
								<?php foreach ($tasks as $task): ?>									
									<tr class="pointer <?php echo ($task->work ==1) ? "text-warning" : ""; ?>">
										<td class="w-200px">
											<a class="btn btn-info btn-xs"
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_modtask" 
											 data-id_task="<?php echo limpiar_datos($task->id_task) ?>"
											 data-name="<?php echo limpiar_datos($task->name) ?>"
											 data-module="<?php echo limpiar_datos($task->module) ?>"
											 data-note="<?php echo limpiar_datos($task->note) ?>"
											 >
												<i class="fa fa-pencil-alt fa-lg fa-fw"></i>
											</a>		
											<a class="btn btn-warning btn-xs"
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_worktask" 
											 data-id_task="<?php echo limpiar_datos($task->id_task) ?>"
											 >
												<i class="fa fa-suitcase fa-lg fa-fw"></i>
											</a>				
											<a class="btn btn-white btn-xs"
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_distask" 
											 data-id_task="<?php echo limpiar_datos($task->id_task) ?>"
											 >
												<i class="fa fa-pause fa-lg fa-fw"></i>
											</a>					
											<a class="btn btn-success btn-xs"
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_donetask" 
											 data-id_task="<?php echo limpiar_datos($task->id_task) ?>"
											 >
												<i class="fa fa-check fa-lg fa-fw"></i>
											</a>							
											<a class="btn btn-danger btn-xs"
											 data-bs-toggle="modal" 
											 data-bs-target="#mdl_deltask" 
											 data-id_task="<?php echo limpiar_datos($task->id_task) ?>"
											 >
												<i class="fa fa-times fa-lg fa-fw"></i>
											</a>					
										</td>
										<td>	
										 	<?php if($task->urgent == 1): ?> 
												<i class="fa fa-bell fa-sm fa-fw text-warning"></i>
											<?php endif; ?>
											<?php if($task->module == "DB"): ?> 
												<i class="fa fa-database fa-sm fa-fw text-info"></i>
											<?php endif; ?>
											<?php if($task->module == "FTP"): ?> 
												<i class="fa fa-folder fa-sm fa-fw text-info"></i>
											<?php endif; ?>
											<?php if($task->note <> ""): ?> 
												<i class="fa fa-lightbulb fa-sm fa-fw text-info" 
												data-toggle="tooltip" 
										 		data-placement="top" 
										 		title="<?php echo limpiar_datos($task->note) ?>"
												></i>
											<?php endif; ?>
											<?php echo limpiar_datos($task->name); ?>
										</td>	
										<td>
											<?php echo limpiar_datos($task->module); ?>
										</td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table> 
					</div>
					<!-- END panel-body -->
				</div>
				<!-- END panel -->

				<!-- BEGIN panel -->
				<div class="panel panel-inverse mt-3">
					<div class="panel-heading">
						<h4 class="panel-title">Tasks recently done</h4>
					</div>
					<!-- BEGIN panel-body -->
					<div class="panel-body">
						<table class="table table-hover table-sm">
							<thead>
								<tr>
									<th class="text-start">Name</th>
									<th class="text-start">Module</th>
								</tr>
							</thead>
							<tbody class="fs-14px">
								<?php foreach ($tasksdone as $taskdone): ?>
									<tr class="text-success">
										<td><?php echo limpiar_datos($taskdone->name); ?></td>
										<td><?php echo limpiar_datos($taskdone->module); ?></td>
									</tr>
								<?php endforeach; ?>
							</tbody>
						</table> 
					</div>
					<!-- END panel-body -->
				</div>
				<!-- END panel -->

				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_createproject">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<div class="modal-header">
								<h4 class="modal-title">Create project</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">
								<div class="col">
									<div class="form-floating">
										<input type="text" class="form-control text-uppercase fs-15px" name="name_project" id="name_project" value="<?php echo @recover_var($name_project) ?>"/>
										<label for="name_project" class="d-flex align-items-center fs-15px">
											Project:
										</label>
									</div>
								</div>
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_create" name="sub_create" class="btn btn-success">
									<i class="fa fa-plus fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->

				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_modtask">
					<div class="modal-dialog modal-lg modal-dialog-centered">
						<div class="modal-content">
							<input type="hidden" id="modtask_id_task" name="modtask_id_task">
							<input type="hidden" id="modtask_id_project" name="modtask_id_project">
							
							<div class="modal-header">
								<h4 class="modal-title">
									Updating task: <span id="modtask_name"></span> / <span id="modtask_module"></span>
								</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">								
								<!-- BEGIN row -->
								<div class="row">
									<div class="col">
										<div class="form-floating">
											<input type="text" class="form-control fs-15px" name="edit_name" id="edit_name"/>
											<label for="edit_name" class="d-flex align-items-center fs-15px">
												Name:
											</label>
										</div>
									</div>
								</div>
								<!-- END row -->
								<!-- BEGIN row -->
								<div class="row mt-3">
									<div class="col">
										<div class="form-floating">
											<input type="text" class="form-control fs-15px text-uppercase" name="edit_module" id="edit_module"/>
											<label for="edit_module" class="d-flex align-items-center fs-15px">
												Module:
											</label>
										</div>
									</div>
								</div>
								<!-- END row -->
								<!-- BEGIN row -->
								<div class="row mt-3">
									<div class="col">
										<div class="form-floating">
											<input type="text" class="form-control fs-15px" name="edit_note" id="edit_note"/>
											<label for="edit_note" class="d-flex align-items-center fs-15px">
												Note:
											</label>
										</div>
									</div>
								</div>
								<!-- END row -->
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_urgtask" name="sub_urgtask" class="btn btn-warning">
									<i class="fa fa-bell fa-lg fa-fw"></i>
								</button>
								<button type="submit" id="sub_modtask" name="sub_modtask" class="btn btn-primary">
									<i class="fa fa-check fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->		
				
				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_worktask">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<input type="hidden" id="work_idtask" name="work_idtask">
							
							<div class="modal-header">
								<h4 class="modal-title">Change state</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">
								<p>Are you sure you want to change the state of this task?</p>
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_worktask" name="sub_worktask" class="btn btn-warning">
									<i class="fa fa-check fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->
				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_distask">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<input type="hidden" id="dis_idtask" name="dis_idtask">
							
							<div class="modal-header">
								<h4 class="modal-title">Change state</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">
								<p>Are you sure you want to change the state of this task?</p>
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_distask" name="sub_distask" class="btn btn-white">
									<i class="fa fa-check fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->
				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_donetask">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<input type="hidden" id="done_idtask" name="done_idtask">
							
							<div class="modal-header">
								<h4 class="modal-title">Change state</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">
								<p>Are you sure you want to change the state of this task?</p>
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_donetask" name="sub_donetask" class="btn btn-success">
									<i class="fa fa-check fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->
				<!-- BEGIN #modal-dialog -->
				<div class="modal fade" id="mdl_deltask">
					<div class="modal-dialog modal-dialog-centered">
						<div class="modal-content">
							<input type="hidden" id="del_idtask" name="del_idtask">
							
							<div class="modal-header">
								<h4 class="modal-title">Change state</h4>
								<button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
							</div>
							<div class="modal-body">
								<p>Are you sure you want to change the state of this task?</p>
							</div>
							<div class="modal-footer">
								<a href="javascript:;" class="btn btn-white" data-bs-dismiss="modal">
									<i class="fa fa-arrow-left fa-lg fa-fw"></i>
								</a>
								<button type="submit" id="sub_deltask" name="sub_deltask" class="btn btn-danger">
									<i class="fa fa-check fa-lg fa-fw"></i>
								</button>
							</div>
						</div>
					</div>
				</div>
				<!-- END #modal-dialog -->
			</form>
		</div>
		<!-- END #content -->
		
		<!-- BEGIN scroll-top-btn -->
		<a href="javascript:;" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
		<!-- END scroll-top-btn -->
	</div>
	<!-- END #app -->

	<!-- ================== BEGIN core-js ================== -->
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
	<!-- ================== END core-js ================== -->

	<script src="<?php echo htmlspecialchars(RUTA) ?>resources/js/fab.js"></script>

	<script type="text/javascript">
		$(function () {
			$('[data-toggle="tooltip"]').tooltip()
		})

		$('#mdl_createproject').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
		
			var name_project = document.getElementById('name_project');
		
			name_project.focus();
		})
		
		$('#mdl_modtask').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var recipient_id_task = button.data('id_task');
			var recipient_name = button.data('name');
			var recipient_module = button.data('module');
			var recipient_note = button.data('note');
		
			var id_task = document.getElementById('modtask_id_task');
			var name = document.getElementById('modtask_name');
			var module = document.getElementById('modtask_module');
			var editname = document.getElementById('edit_name');
			var editmodule = document.getElementById('edit_module');
			var editnote = document.getElementById('edit_note');
		
			id_task.value = recipient_id_task;
			name.innerHTML = recipient_name;
			module.innerHTML = recipient_module;
			editname.value = recipient_name;
			editmodule.value = recipient_module;
			editnote.value = recipient_note;
		})

		$('#mdl_worktask').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var recipient_id_task = button.data('id_task');
		
			var id_task = document.getElementById('work_idtask');
		
			id_task.value = recipient_id_task;
		})

		$('#mdl_distask').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var recipient_id_task = button.data('id_task');

			var id_task = document.getElementById('dis_idtask');

			id_task.value = recipient_id_task;
		})

		$('#mdl_donetask').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var recipient_id_task = button.data('id_task');

			var id_task = document.getElementById('done_idtask');

			id_task.value = recipient_id_task;
		})

		$('#mdl_deltask').on('shown.bs.modal', function (event) {
			var button = $(event.relatedTarget);
			var recipient_id_task = button.data('id_task');

			var id_task = document.getElementById('del_idtask');

			id_task.value = recipient_id_task;
		})

		pressenterandfocus('name','module');
		pressenterandfocus('module','note');
		pressenterandclick('note','sub_add');
	</script>
</body>
</html>