<?php
#region region DOCS
/** @var Partido $modpartido */
/** @var ApuestaTipo[] $apuestastipos */
/** @var Pais[] $paises */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Partidos</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <!-- BEGIN HEAD -->
    <?php require_once __ROOT__ . '/views/head.view.php';?>	
    <link href="<?php echo htmlspecialchars(RUTA) ?>resources/assets/plugins/bootstrap-datepicker/dist/css/bootstrap-datepicker.css" rel="stylesheet"/>
    <link id="region_CSS_slider" href="<?php echo RUTA ?>resources/assets/plugins/ion-rangeslider/css/ion.rangeSlider.min.css" rel="stylesheet" />
    <!-- END HEAD -->
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Editar partido</h1>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="epartido" method="POST">
            <input type="hidden" id="idpartido" name="idpartido" value="<?php echo @recover_var($idpartido) ?>">

            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="home" class="d-flex align-items-center fs-12px">
                        Home:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px text-uppercase no-border-radious" name="home" id="home" value="<?php echo @recover_var($modpartido->home) ?>" />
                </div>
                <!-- END text -->
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="away" class="d-flex align-items-center fs-12px">
                        Away:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px text-uppercase no-border-radious" name="away" id="away" value="<?php echo @recover_var($modpartido->away) ?>" />
                </div>
                <!-- END text -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN text -->
                <div class="col-md-6 col-xs-12">
                    <label for="pais" class="d-flex align-items-center fs-12px">
                        Pais:
                    </label>
                    <input type="text" class="form-control form-control-fh text-uppercase fs-12px no-border-radious" name="pais" id="pais" value="<?php echo @recover_var($modpartido->pais) ?>" onclick="this.focus();this.select('');" />
                </div>
                <!-- END text -->
                <!-- BEGIN date -->
                <div class="col-md-6 col-xs-12">
                    <label for="fecha" class="d-flex align-items-center fs-12px">
                        Fecha:
                    </label>
                    <input type="text" class="form-control form-control-fh fs-12px no-border-radious datepicker" id="fecha" name="fecha" value="<?php echo @recover_var($modpartido->fecha) ?>" autocomplete="off"/>
                </div>
                <!-- END date -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN slider -->
                <div class="col-md-12 col-xs-12">
                    <label for="hora" class="fs-15px">
                        Hora:
                    </label>
                    <input type="text" id="hora" name="hora" value="<?php echo @recover_var($modpartido->hora) ?>" />
                </div>
                <!-- END slider -->
            </div>
            <!-- END ROW -->
            <!-- ROW -->
            <div class="row mt-3">
                <!-- BEGIN link -->
                <div class="col-md-4 col-xs-12">
                    <a href="lpartidos" class="btn btn-xs btn-default w-100">
                        Regresar
                    </a>
                </div>
                <!-- END link -->
                <!-- sub_mod -->
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_mod" name="sub_mod" class="region_SUBMIT_sub_mod btn btn-xs btn-success w-100">
                        Modificar
                    </button>
                </div>
                <!-- END sub_mod -->
            </div>
            <!-- END ROW -->
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script id="region_JS" src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<!-- BEGIN JS date -->
<script id="region_JS_date" src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-datepicker/dist/js/bootstrap-datepicker.js"></script>
<script src="<?php echo RUTA ?>resources/js/datepickerini.js"></script>
<!-- END JS date -->
<!-- BEGIN JS slider -->
<script src="<?php echo RUTA ?>resources/assets/plugins/ion-rangeslider/js/ion.rangeSlider.min.js"></script>
<script>
  $("#hora").ionRangeSlider({
    min: 0,
    max: 23,
    maxPostfix: "+",
    prettify: false,
    hasGrid: true,
    skin: "big"		  
  });
</script>
<!-- END JS slider -->
<!-- BEGIN JS autocomplete -->
<script id="region_JS_autocomplete_pais" type="text/javascript">
  $("#pais").autocomplete({
    source: [
        <?php foreach ($paises as $pais): ?>
            "<?php echo $pais->nombre; ?>",	 
        <?php endforeach; ?>		
    ]
  });
</script>
<!-- BEGIN JS autocomplete -->
<?php #endregion JS ?>

</body>
</html>