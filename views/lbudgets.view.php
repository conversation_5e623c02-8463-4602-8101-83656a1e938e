<?php
#region region DOCS
/** @var Budget $newbudget */
/** @var Budget[] $budgets */
#endregion docs
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Canales</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <?php #endregion head ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <?php require_once __ROOT__ . '/views/labels.view.php'; ?>

        <!-- BEGIN page-header -->
        <h1 class="page-header">Canales</h1>
        <!-- END page-header -->

        <?php #region region FORM ?>
        <form action="lbudgets" method="POST">
            <input type="hidden" id="selidbudget" name="selidbudget">

            <?php #region region SUBMIT sub_editbudget ?>
            <div class="col" style="display: none">
                <button type="submit" id="sub_editbudget" name="sub_editbudget" class="btn btn-success w-100">
                    sub_editbudget
                </button>
            </div>
            <?php #endregion sub_editbudget ?>

            <!-- BEGIN row -->
            <div class="row">
                <!-- BEGIN text -->
	            <div class="col-md-12 col-xs-12">
		            <div class="mb-3">
			            <label class="form-label">Canal:</label>
			            <input type="text" name="canal" id="canal" value="<?php echo @recover_var($newbudget->canal) ?>" class="form-control" autofocus/>
		            </div>
	            </div>
                <!-- END text -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row">
                <?php #region region SUBMIT sub_add ?>
                <div class="col-md-12 col-xs-12">
                    <button type="submit" id="sub_add" name="sub_add" class="btn btn-success w-100">
                        Agregar
                    </button>
                </div>
                <?php #endregion sub_add ?>
            </div>
            <!-- END row -->
            <?php #region region PANEL canales ?>
            <div class="panel panel-inverse mt-3">
                <div class="panel-heading">
                    <h4 class="panel-title">
                        Canales:
                    </h4>
                </div>
                <!-- BEGIN panel-body -->
                <div class="panel-body table-nowrap" style="overflow: auto">
                    <?php #region region TABLE canales ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="w-50px"></th>
                            <th>ID</th>
                            <th>Canal</th>
                            <th>Valor</th>
                        </tr>
                        </thead>
                        <tbody class="fs-14px">
                        <?php #region region ARRAY canales ?>
                        <?php foreach ($budgets as $budget): ?>
                            <tr>
                                <td>
                                    <i class="fa fa-pencil fa-md cursor-pointer" onclick="editbudget('<?php echo limpiar_datos($budget->id_budget); ?>');"></i>
                                    <i class="fa fa-trash fa-md cursor-pointer text-danger ms-1" data-bs-toggle="modal" data-bs-target="#mdl_delbudget" data-idbudget="<?php echo limpiar_datos($budget->id_budget) ?>"></i>
                                </td>
                                <td class="text-center"><?php echo ordena($budget->id_budget); ?></td>
                                <td><?php echo $budget->canal; ?></td>
                                <td class="text-end">$<?php echo format_currency($budget->valor); ?></td>
                            </tr>
                        <?php endforeach; ?>
                        <?php #endregion array canales ?>
                        </tbody>
                    </table>
                    <?php #endregion table canales ?>
                </div>
                <!-- END panel-body -->
            </div>
            <?php #endregion panel canales ?>
        </form>
        <?php #endregion form ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<?php #region region MODAL mdl_delbudget ?>
<div class="modal fade" id="mdl_delbudget">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <input type="hidden" id="mdl_delbudget_idbudget" name="mdl_delbudget_idbudget">

            <div class="modal-header">
                <h4 class="modal-title">Eliminar canal</h4>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-hidden="true"></button>
            </div>
            <div class="modal-body">
                <p>Esta seguro que desea eliminar este canal?</p>
            </div>
            <div class="modal-footer">
                <a href="#" class="btn btn-white" data-bs-dismiss="modal">
                    <i class="fa fa-arrow-left fa-lg fa-fw"></i>
                </a>
                <button type="submit" id="sub_delbudget" name="sub_delbudget" class="btn btn-danger">
                    <i class="fa fa-check fa-lg fa-fw"></i>
                </button>
            </div>
        </div>
    </div>
</div>
<?php #endregion mdl_delbudget ?>
<?php #region region JS mdl_delbudget ?>
<script type="text/javascript">
    $('#mdl_delbudget').on('shown.bs.modal', function (event) {
        const button = $(event.relatedTarget);
        const recipient_idbudget = button.data('idbudget');

        const mdl_delbudget_idbudget = document.getElementById('mdl_delbudget_idbudget');

        mdl_delbudget_idbudget.value = recipient_idbudget;
    })
</script>
<?php #endregion js mdl_delbudget ?>
<?php #region region JS editbudget ?>
<script type="text/javascript">
    function editbudget($idbudget) {
        const selidbudget = document.getElementById('selidbudget');
        selidbudget.value = $idbudget;

        document.getElementById('sub_editbudget').click();
    }
</script>
<?php #endregion js editbudget ?>
<?php #endregion js ?>

</body>
</html>