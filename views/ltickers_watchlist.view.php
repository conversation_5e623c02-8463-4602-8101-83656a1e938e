<?php
#region region DOCS
/** @var TickerWatchlist[] $tickers_watchlist */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Watchlist</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>
	
	<?php #region HEAD ?>
	<?php require_once __ROOT__ . '/views/head.view.php'; ?>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
	<?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
		<?php #region region PAGE HEADER ?>
        <h4>Watchlist</h4>

        <hr>
		<?php #endregion PAGE HEADER ?>
		
		<?php #region region FORM ?>
        <form action="ltickers_watchlist" method="POST" enctype="multipart/form-data">
            <input type="hidden" id="sel_ticker" name="sel_ticker">
	        
	        <?php #region region SUBMIT sub_vetar ?>
            <div class="col" style="display: none">
                <button type="submit" id="sub_vetar" name="sub_vetar" class="btn btn-success w-100">
                    sub_vetar
                </button>
            </div>
	        <?php #endregion sub_vetar ?>
            
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN file -->
                <div class="col-md-12 col-xs-12">
                    <span class="input-group-text no-border-radious bg-gray fs-12px w-100">
                        Archivo CSV:
                    </span>
                    <div class="input-group">
                        <input type="file" id="archivo_csv" name="archivo_csv" class="form-control form-control-fh form-control-fh-file no-border-radious"/>
                    </div>
                </div>
                <!-- END file -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
	            <?php #region region SUBMIT sub_subir ?>
                <div class="col-md-6 col-xs-12">
                    <button type="submit" id="sub_subir" name="sub_subir" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Subir
                    </button>
                </div>
	            <?php #endregion SUBMIT sub_subir ?>
	            <?php #region region SUBMIT sub_exportar ?>
                <div class="col-md-6 col-xs-12">
                    <button type="submit" id="sub_exportar" name="sub_exportar" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Exportar
                    </button>
                </div>
	            <?php #endregion SUBMIT sub_exportar ?>
            </div>
            <!-- END row -->
            <?php #region region PANEL watchlist ?>
            <div class="panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Watchlist:
                        <span class="badge bg-primary rounded-0 fs-11px">
                            <?php echo count($tickers_watchlist); ?>
                        </span>
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="p-1 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE watchlist ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Ticker</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php foreach ($tickers_watchlist as $ticker_watchlist): ?>
                            <tr class="cursor-pointer <?php echo ($ticker_watchlist->estado == 0) ? "bg-danger" : ""; ?>" onclick="vetar('<?php echo limpiar_datos($ticker); ?>');">
                                <td class="text-center"><?php echo $ticker_watchlist->ticker; ?></td>
                            </tr>
                        <?php endforeach; ?>
                        </tbody>
                    </table>
                    <?php #endregion TABLE watchlist ?>
                </div>
                <!-- END PANEL body -->
            </div>
            <?php #endregion PANEL watchlist ?>
        </form>
		<?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->

<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $success_text; ?>",
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>
<?php #region region JS vetar ?>
<script type="text/javascript">
    function vetar($ticker) {
        const sel_ticker = document.getElementById('sel_ticker');
        sel_ticker.value = $ticker;

        document.getElementById('sub_vetar').click();
    }
</script>
<?php #endregion js vetar ?>

<?php #endregion JS ?>

</body>
</html>