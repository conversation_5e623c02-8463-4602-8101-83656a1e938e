<?php
#region region DOCS
/** @var array $reporte */
/** @var string $fecharange */
#endregion DOCS
?>
<!DOCTYPE html>
<html lang="en" class="dark-mode">
<head>
    <meta charset="utf-8"/>
    <title>My Dash | Reportes</title>
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
    <meta content="" name="description"/>
    <meta content="" name="author"/>

    <?php #region HEAD ?>
    <?php require_once __ROOT__ . '/views/head.view.php'; ?>
    <link href="<?php echo RUTA ?>resources/assets/plugins/bootstrap-daterangepicker/daterangepicker.css" rel="stylesheet"/>
    <?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
    <span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed app-without-sidebar app-with-top-menu">
    <!-- #header -->
    <?php require_once __ROOT__ . '/views/header.view.php'; ?>

    <!-- #sidebar -->
    <?php require_once __ROOT__ . '/views/topbar.view.php'; ?>

    <!-- BEGIN #content -->
    <div id="content" class="app-content">
        <!-- BEGIN page-header -->
        <h4>Reporte de fallas</h4>

        <hr>
        <!-- END page-header -->

        <?php #region FORM ?>
        <form action="cpartidoreporte_fallas" method="POST">
            <!-- BEGIN row -->
            <div class="row mt-3">
                <!-- BEGIN daterange -->
                <div class="col-md-12 col-xs-12">
                    <div id="fecha">
                        <label for="fecharange" class="d-flex align-items-center fs-12px">
                            Fecha:
                        </label>
                        <input type="text" class="form-control form-control-fh fs-12px no-border-radious" id="fecharange" name="fecharange" value="<?php echo @recover_var($fecharange) ?>"/>
                    </div>
                </div>
                <!-- END daterange -->
            </div>
            <!-- END row -->
            <!-- BEGIN row -->
            <div class="row mt-3">
                <?php #region region SUBMIT sub_search ?>
                <div class="col-md-8 col-xs-12">
                    <button type="submit" id="sub_search" name="sub_search" class="btn btn-xs btn-primary w-100 no-border-radious">
                        Buscar
                    </button>
                </div>
                <?php #endregion SUBMIT sub_search ?>
                <?php #region region LINK regresar ?>
                <div class="col-md-4 col-xs-12">
                    <a href="lpartidosreportes" class="btn btn-xs btn-default w-100 no-border-radious">
                        Regresar
                    </a>
                </div>
                <?php #endregion LINK regresar ?>
            </div>
            <!-- END row -->
            <?php #region region PANEL reporte ?>
            <div class="panel panel-inverse mt-3 no-border-radious">
                <div class="panel-heading no-border-radious">
                    <h4 class="panel-title">
                        Reporte:
                    </h4>
                </div>
                <!-- BEGIN PANEL body -->
                <div class="p-0 table-nowrap" style="overflow: auto">
                    <?php #region region TABLE reporte ?>
                    <table class="table table-hover table-sm">
                        <thead>
                        <tr>
                            <th class="text-center">Criterio</th>
                            <th class="text-center">W</th>
                            <th class="text-center">L</th>
                            <th class="text-center">D</th>
                        </tr>
                        </thead>
                        <tbody class="fs-12px">
                        <?php for ($i = 0; $i < count($reporte); $i++): ?>
                            <?php if ($reporte[$i]['wins'] > 0 || $reporte[$i]['losses'] > 0): ?>
                            <tr class="<?php echo $reporte[$i]['bgcolor']; ?>">
                                <td><?php echo $reporte[$i]['criterio']; ?></td>
                                <td class="text-center"><?php echo $reporte[$i]['wins']; ?></td>
                                <td class="text-center"><?php echo $reporte[$i]['losses']; ?></td>
                                <td class="text-center">
                                    <?php echo $reporte[$i]['diff']; ?>
                                </td>
                            </tr>
                            <?php endif; ?>
                        <?php endfor; ?>
                        </tbody>
                    </table>
                    <?php #region TABLE reporte ?>
                </div>
                <!-- END PANEL body -->
            </div>
            <?php #endregion PANEL reporte ?>
        </form>
        <?php #endregion FORM ?>
    </div>
    <!-- END #content -->

    <!-- BEGIN scroll-top-btn -->
    <a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
    <!-- END scroll-top-btn -->
</div>
<!-- END #app -->

<?php #region region JS ?>
<!-- ================== BEGIN core-js ================== -->
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/vendor.min.js"></script>
<script src="<?php echo htmlspecialchars(RUTA) ?>resources/assets/js/app.min.js"></script>
<!-- ================== END core-js ================== -->
<script src="<?php echo RUTA ?>resources/assets/plugins/moment/min/moment.min.js"></script>
<script src="<?php echo RUTA ?>resources/assets/plugins/bootstrap-daterangepicker/daterangepicker.js"></script>

<?php #region region js daterange ?>
<script type="text/javascript">
    $("#fecha").daterangepicker({
        autoApply: true,
        format: "YYYY-MM-DD",
        separator: " to ",
        startDate: moment().subtract("days", 1),
        endDate: moment(),
    }, function (start, end) {
        $("#fecha input").val(start.format("YYYY-MM-DD") + " - " + end.format("YYYY-MM-DD"));
    });
</script>
<?php #endregion js daterange ?>
<?php #region region JS sweet alert import ?>
<script src="<?php echo RUTA ?>resources/assets/plugins/sweetalert/dist/sweetalert.min.js"></script>
<?php #endregion JS sweet alert import ?>
<?php #region region JS sweet alert success ?>
<?php if (!empty($success_text)): ?>
    <script type="text/javascript">
        swal({
            text: '<?php echo $success_text; ?>',
            icon: 'success',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-success',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert success ?>
<?php #region region JS sweet alert error ?>
<?php if (!empty($error_text)): ?>
    <script type="text/javascript">
        swal({
            text: "<?php echo $error_text; ?>",
            icon: 'error',
            buttons: {
                confirm: {
                    text: 'Ok',
                    value: true,
                    visible: true,
                    className: 'btn btn-danger',
                    closeModal: true
                }
            }
        });
    </script>
<?php endif; ?>
<?php #endregion JS sweet alert error ?>

<?php #endregion JS ?>

</body>
</html>